# Definir la ruta base
$basePath = "client\src\assets\atracciones"

# Mapeo de nombres de archivo antiguos a nuevos
$fileMapping = @{
    # Cocha Resbaladero
    "cocha-resbaladero\Cocha1.webp" = "cocha-resbaladero\cocha-resbaladero-1.webp"
    "cocha-resbaladero\Cocha2.webp" = "cocha-resbaladero\cocha-resbaladero-2.webp"
    "cocha-resbaladero\Cocha3.webp" = "cocha-resbaladero\cocha-resbaladero-3.webp"
    "cocha-resbaladero\Cocha4.webp" = "cocha-resbaladero\cocha-resbaladero-4.webp"
    
    # Iglesia San Andrés
    "iglesia-san-andres\Iglesia_San_Andres_Pica1.webp" = "iglesia-san-andres\iglesia-san-andres-1.webp"
    "iglesia-san-andres\Iglesia_San_Andres_Pica2.webp" = "iglesia-san-andres\iglesia-san-andres-2.webp"
    "iglesia-san-andres\Iglesia_San_Andres_Pica3.webp" = "iglesia-san-andres\iglesia-san-andres-3.webp"
    
    # Parque de los Dinosaurios
    "parque-dinosaurios\Parque_dinosaurios1.webp" = "parque-dinosaurios\parque-dinosaurios-1.webp"
    "parque-dinosaurios\Parque_dinosaurios2.webp" = "parque-dinosaurios\parque-dinosaurios-2.webp"
    "parque-dinosaurios\Parque_dinosaurios3.webp" = "parque-dinosaurios\parque-dinosaurios-3.webp"
}

# Copiar archivos de las carpetas antiguas a las nuevas
$filesToCopy = @(
    @{
        Source = "Pueblo-de-pica\pica.webp"
        Destination = "pueblo-de-pica\pueblo-de-pica-1.webp"
    },
    @{
        Source = "Pueblo-de-pica\pica2.webp"
        Destination = "pueblo-de-pica\pueblo-de-pica-2.webp"
    },
    @{
        Source = "Pueblo-de-pica\plaza.webp"
        Destination = "pueblo-de-pica\pueblo-de-pica-3.webp"
    },
    @{
        Source = "Reserva-Pampa-del-Tamarugal\Pampa1.jpg"
        Destination = "reserva-pampa-del-tamarugal\reserva-pampa-del-tamarugal-1.jpg"
    },
    @{
        Source = "Reserva-Pampa-del-Tamarugal\Pampa2.jpg"
        Destination = "reserva-pampa-del-tamarugal\reserva-pampa-del-tamarugal-2.jpg"
    },
    @{
        Source = "Reserva-Pampa-del-Tamarugal\Pampa3.jpg"
        Destination = "reserva-pampa-del-tamarugal\reserva-pampa-del-tamarugal-3.jpg"
    },
    @{
        Source = "Salar-del-Huasco\huasco-salar-1.webp"
        Destination = "salar-del-huasco\salar-del-huasco-1.webp"
    },
    @{
        Source = "Salar-del-Huasco\pn-salar-del-huasco_52703376399_o.jpg"
        Destination = "salar-del-huasco\salar-del-huasco-2.jpg"
    },
    @{
        Source = "Salar-del-Huasco\Salar-del-Huasco1..jpg"
        Destination = "salar-del-huasco\salar-del-huasco-3.jpg"
    }
)

# Renombrar archivos existentes
foreach ($oldPath in $fileMapping.Keys) {
    $oldFullPath = Join-Path -Path $basePath -ChildPath $oldPath
    $newFullPath = Join-Path -Path $basePath -ChildPath $fileMapping[$oldPath]
    
    if (Test-Path $oldFullPath) {
        Write-Host "Renombrando '$oldPath' a '$($fileMapping[$oldPath])'..." -ForegroundColor Yellow
        
        # Verificar si el archivo de destino ya existe
        if (Test-Path $newFullPath) {
            Write-Host "El archivo de destino ya existe. Omitiendo..." -ForegroundColor Yellow
        } else {
            # Renombrar el archivo
            Rename-Item -Path $oldFullPath -NewName $newFullPath
            Write-Host "Archivo renombrado exitosamente." -ForegroundColor Green
        }
    } else {
        Write-Host "El archivo '$oldPath' no existe. Omitiendo..." -ForegroundColor Yellow
    }
}

# Copiar archivos de las carpetas antiguas a las nuevas
foreach ($fileCopy in $filesToCopy) {
    $sourceFullPath = Join-Path -Path $basePath -ChildPath $fileCopy.Source
    $destinationFullPath = Join-Path -Path $basePath -ChildPath $fileCopy.Destination
    
    if (Test-Path $sourceFullPath) {
        Write-Host "Copiando '$($fileCopy.Source)' a '$($fileCopy.Destination)'..." -ForegroundColor Yellow
        
        # Verificar si el archivo de destino ya existe
        if (Test-Path $destinationFullPath) {
            Write-Host "El archivo de destino ya existe. Omitiendo..." -ForegroundColor Yellow
        } else {
            # Copiar el archivo
            Copy-Item -Path $sourceFullPath -Destination $destinationFullPath
            Write-Host "Archivo copiado exitosamente." -ForegroundColor Green
        }
    } else {
        Write-Host "El archivo '$($fileCopy.Source)' no existe. Omitiendo..." -ForegroundColor Yellow
    }
}

Write-Host "Proceso completado." -ForegroundColor Cyan

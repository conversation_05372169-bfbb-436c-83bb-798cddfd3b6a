import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useScrollPosition from '@/hooks/use-scroll-position';
import { format } from 'date-fns';

const Header = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const scrollY = useScrollPosition();
  const headerBg = scrollY > 50 ? 'bg-woodBrown-dark shadow-lg py-3' : 'py-6';

  // Close mobile menu if window resizes to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024 && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [mobileMenuOpen]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (mobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [mobileMenuOpen]);

  const scrollToSection = (sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
      setMobileMenuOpen(false);
    }
  };

  return (
    <>
      <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${headerBg}`}>
        <nav className="container mx-auto px-4 flex justify-between items-center">
          <a 
            href="#inicio" 
            className="flex items-center"
            onClick={(e) => {
              e.preventDefault();
              scrollToSection('inicio');
            }}
          >
            <div className="h-10 w-10 mr-3 bg-amber-500 rounded-full flex items-center justify-center">
              <span className="material-icons text-woodBrown-dark">cabin</span>
            </div>
            <span className="text-xl md:text-2xl font-serif font-bold text-white text-shadow-dark">
              <span className="text-amber-400">LA POSADA</span> DEL OSO
            </span>
          </a>
          
          {/* Desktop Navigation */}
          <div className="hidden lg:flex space-x-8 items-center">
            {['inicio', 'cabana', 'ubicacion', 'galeria', 'contacto'].map(section => (
              <a 
                key={section}
                href={`#${section}`}
                className="text-white hover:text-amber-400 transition-all font-rustic uppercase font-medium text-shadow relative group"
                onClick={(e) => {
                  e.preventDefault();
                  scrollToSection(section);
                }}
              >
                {section.charAt(0).toUpperCase() + section.slice(1)}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-amber-400 transition-all duration-300 group-hover:w-full"></span>
              </a>
            ))}
            
            <a 
              href="#reservar" 
              className="ml-4 px-6 py-2 bg-amber-500 hover:bg-amber-400 text-woodBrown-dark rounded-full shadow-premium transition-all transform hover:-translate-y-0.5 hover:shadow-glow font-rustic uppercase font-bold"
              onClick={(e) => {
                e.preventDefault();
                scrollToSection('reservar');
              }}
            >
              Reservar
            </a>
          </div>
          
          {/* Mobile Navigation Trigger */}
          <button 
            className="lg:hidden text-white focus:outline-none" 
            onClick={() => setMobileMenuOpen(true)}
          >
            <span className="material-icons">menu</span>
          </button>
        </nav>
      </header>
      
      {/* Mobile Navigation Menu */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div 
            className="fixed inset-0 bg-woodBrown-dark bg-opacity-95 z-50 lg:hidden"
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'tween', duration: 0.3 }}
          >
            <div className="flex justify-end p-6">
              <button 
                className="text-white focus:outline-none" 
                onClick={() => setMobileMenuOpen(false)}
              >
                <span className="material-icons">close</span>
              </button>
            </div>
            
            <div className="flex flex-col items-center justify-center h-full">
              {['inicio', 'cabana', 'ubicacion', 'galeria', 'contacto'].map(section => (
                <a 
                  key={section}
                  href={`#${section}`}
                  className="text-2xl text-white hover:text-amber-400 py-4 transition-colors font-serif"
                  onClick={(e) => {
                    e.preventDefault();
                    scrollToSection(section);
                  }}
                >
                  {section.charAt(0).toUpperCase() + section.slice(1)}
                </a>
              ))}
              
              <a 
                href="#reservar" 
                className="mt-6 px-8 py-3 bg-amber-500 hover:bg-amber-400 text-woodBrown-dark rounded-sm shadow-lg text-xl font-medium transition-all transform hover:-translate-y-0.5"
                onClick={(e) => {
                  e.preventDefault();
                  scrollToSection('reservar');
                }}
              >
                Reservar
              </a>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Header;

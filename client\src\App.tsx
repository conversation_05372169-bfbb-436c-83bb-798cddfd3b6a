import { useState, useEffect } from "react";
import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import Home from "@/pages/Home";
import NotFound from "@/pages/not-found";
import Preloader from "@/components/custom/Preloader";
import ImageValidator from "./components/dev/ImageValidator";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  const [loading, setLoading] = useState(true);

  // Handle preloader complete
  const handleLoadComplete = () => {
    setLoading(false);
  };

  // Ensure fonts are loaded before showing content
  useEffect(() => {
    document.fonts.ready.then(() => {
      // Add a minimum delay for the preloader to be visible
      const minDelay = setTimeout(() => {
        // The preloader component will call handleLoadComplete when it's done
      }, 1000);

      return () => clearTimeout(minDelay);
    });
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Preloader onLoadComplete={handleLoadComplete} />
        {!loading && <Router />}
        {/* Componente de validación de imágenes (solo visible en desarrollo) */}
        {process.env.NODE_ENV === 'development' && <ImageValidator />}
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;

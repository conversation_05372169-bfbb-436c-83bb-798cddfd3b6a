// Script para generar certificados SSL autofirmados para desarrollo
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

// Crear directorio para certificados si no existe
const certsDir = path.join(process.cwd(), 'certs');
if (!fs.existsSync(certsDir)) {
  fs.mkdirSync(certsDir);
}

console.log('Generando certificados SSL autofirmados para desarrollo...');

try {
  // Generar clave privada
  execSync('openssl genrsa -out certs/key.pem 2048');
  
  // Generar certificado autofirmado
  execSync('openssl req -new -x509 -key certs/key.pem -out certs/cert.pem -days 365 -subj "/CN=localhost"');
  
  console.log('Certificados generados correctamente en el directorio "certs/"');
} catch (error) {
  console.error('Error al generar certificados:', error.message);
  process.exit(1);
}

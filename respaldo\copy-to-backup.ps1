# Script para copiar archivos del proyecto a la carpeta de respaldo
# Excluye la propia carpeta de respaldo para evitar recursión

$sourceDir = Get-Location
$backupDir = Join-Path -Path $sourceDir -ChildPath "respaldo"

# Crear la carpeta de respaldo si no existe
if (-not (Test-Path -Path $backupDir)) {
    New-Item -Path $backupDir -ItemType Directory -Force
}

# Obtener todos los archivos y carpetas, excluyendo la carpeta de respaldo y node_modules
$items = Get-ChildItem -Path $sourceDir -Exclude @("respaldo", "node_modules", ".git", ".venv") -Force

foreach ($item in $items) {
    $destination = Join-Path -Path $backupDir -ChildPath $item.Name

    if ($item.PSIsContainer) {
        # Es un directorio, copiarlo recursivamente
        Write-Host "Copiando directorio: $($item.Name)"
        Copy-Item -Path $item.FullName -Destination $destination -Recurse -Force -ErrorAction SilentlyContinue
    } else {
        # Es un archivo, copiarlo
        Write-Host "Copiando archivo: $($item.Name)"
        Copy-Item -Path $item.FullName -Destination $destination -Force -ErrorAction SilentlyContinue
    }
}

# Crear directorios específicos que necesitamos en el respaldo
$dirsToCreate = @(
    "respaldo/node_modules",
    "respaldo/.git",
    "respaldo/.venv"
)

foreach ($dir in $dirsToCreate) {
    if (-not (Test-Path -Path $dir)) {
        New-Item -Path $dir -ItemType Directory -Force
        Write-Host "Creado directorio vacío: $dir"
    }
}

Write-Host "Copia de respaldo completada en: $backupDir"

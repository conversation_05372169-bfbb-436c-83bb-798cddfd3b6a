import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

type GalleryCategory = 'all' | 'configuracion-2' | 'configuracion-3' | 'exteriores' | 'entorno';

interface GalleryItem {
  image: string;
  alt: string;
  category: Exclude<GalleryCategory, 'all'>;
  height: 'h-56' | 'h-64' | 'h-80';
}

const GaleriaSection = () => {
  const [activeFilter, setActiveFilter] = useState<GalleryCategory>('all');
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImage, setCurrentImage] = useState<number>(0);
  const [visibleItems, setVisibleItems] = useState<GalleryItem[]>([]);

  // Importar imágenes de la cabaña
  const cabanaBasePath = "/src/assets/cabaña/";

  const galleryItems: GalleryItem[] = [
    // Habitaciones
    {
      image: `${cabanaBasePath}piezas/pieza1.jpg`,
      alt: "Dormitorio principal - Habitación 1",
      category: "configuracion-2",
      height: "h-64"
    },
    {
      image: `${cabanaBasePath}piezas/pieza2.jpg`,
      alt: "Dormitorio secundario - Habitación 2",
      category: "configuracion-2",
      height: "h-80"
    },
    {
      image: `${cabanaBasePath}piezas/pieza3.jpg`,
      alt: "Dormitorio adicional - Habitación 3",
      category: "configuracion-3",
      height: "h-64"
    },
    {
      image: `${cabanaBasePath}piezas/pieza4.jpg`,
      alt: "Dormitorio adicional - Habitación 4",
      category: "configuracion-3",
      height: "h-56"
    },

    // Áreas comunes
    {
      image: `${cabanaBasePath}living.jpg`,
      alt: "Sala de estar de la cabaña",
      category: "configuracion-2",
      height: "h-64"
    },
    {
      image: `${cabanaBasePath}cocina.jpg`,
      alt: "Área de cocina completamente equipada",
      category: "configuracion-2",
      height: "h-80"
    },
    {
      image: `${cabanaBasePath}baño.jpg`,
      alt: "Baño completo con ducha",
      category: "configuracion-2",
      height: "h-64"
    },
    {
      image: `${cabanaBasePath}mesa-pool.jpg`,
      alt: "Área de recreación con mesa de pool",
      category: "configuracion-3",
      height: "h-80"
    },

    // Piscina y exteriores
    {
      image: `${cabanaBasePath}piscina/piscina1.jpg`,
      alt: "Vista de la piscina privada",
      category: "exteriores",
      height: "h-56"
    },
    {
      image: `${cabanaBasePath}piscina/piscina2.jpg`,
      alt: "Piscina con área de descanso",
      category: "exteriores",
      height: "h-64"
    },
    {
      image: `${cabanaBasePath}piscina/piscina4.jpg`,
      alt: "Piscina con vista al jardín",
      category: "exteriores",
      height: "h-80"
    },
    {
      image: `${cabanaBasePath}piscina/piscina5.jpg`,
      alt: "Vista panorámica de la piscina",
      category: "exteriores",
      height: "h-64"
    }
  ];

  // Filter items based on active filter
  useEffect(() => {
    if (activeFilter === 'all') {
      setVisibleItems(galleryItems);
    } else {
      setVisibleItems(galleryItems.filter(item => item.category === activeFilter));
    }
  }, [activeFilter]);

  const handleFilterClick = (filter: GalleryCategory) => {
    setActiveFilter(filter);
  };

  const openLightbox = (index: number) => {
    document.body.style.overflow = 'hidden';
    setCurrentImage(index);
    setLightboxOpen(true);
  };

  const closeLightbox = () => {
    document.body.style.overflow = '';
    setLightboxOpen(false);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setCurrentImage(prev => (prev === 0 ? visibleItems.length - 1 : prev - 1));
    } else {
      setCurrentImage(prev => (prev === visibleItems.length - 1 ? 0 : prev + 1));
    }
  };

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!lightboxOpen) return;

      if (e.key === 'Escape') {
        closeLightbox();
      } else if (e.key === 'ArrowLeft') {
        navigateImage('prev');
      } else if (e.key === 'ArrowRight') {
        navigateImage('next');
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [lightboxOpen]);

  return (
    <section id="galeria" className="py-20 bg-linen relative">
      {/* Background texture */}
      <div
        className="texture-background"
      ></div>

      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <motion.h2
            className="text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-woodBrown-dark mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5 }}
          >
            Galería
          </motion.h2>
          <motion.p
            className="text-lg text-stoneGray-dark max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Explora nuestra cabaña a través de imágenes que capturan su esencia y belleza.
          </motion.p>
        </div>

        {/* Gallery Filters */}
        <motion.div
          className="flex flex-wrap justify-center mb-10 gap-2"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          {[
            { id: 'all', label: 'Todas' },
            { id: 'configuracion-2', label: 'Configuración 2 Dormitorios' },
            { id: 'configuracion-3', label: 'Configuración 3 Dormitorios' },
            { id: 'exteriores', label: 'Exteriores' },
            { id: 'entorno', label: 'Entorno' }
          ].map(filter => (
            <button
              type="button"
              key={filter.id}
              className={`px-4 py-2 rounded-sm transition-colors ${
                activeFilter === filter.id
                  ? 'bg-woodBrown text-white'
                  : 'bg-white text-woodBrown hover:bg-stoneGray-light'
              }`}
              onClick={() => handleFilterClick(filter.id as GalleryCategory)}
            >
              {filter.label}
            </button>
          ))}
        </motion.div>

        {/* Masonry Gallery */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <AnimatePresence>
            {visibleItems.map((item, index) => (
              <motion.div
                key={item.image}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.4 }}
                className="gallery-item"
              >
                <div
                  className={`rounded-lg overflow-hidden shadow-xl ${item.height} cursor-pointer hover:shadow-2xl transition-shadow`}
                  onClick={() => openLightbox(index)}
                >
                  <img
                    src={item.image}
                    alt={item.alt}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Lightbox */}
        <AnimatePresence>
          {lightboxOpen && (
            <motion.div
              className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={closeLightbox}
            >
              <button type="button" className="absolute top-6 right-6 text-white text-4xl z-10" onClick={closeLightbox}>
                ×
              </button>

              <button
                type="button"
                className="absolute left-6 top-1/2 transform -translate-y-1/2 text-white text-4xl z-10"
                onClick={(e) => {
                  e.stopPropagation();
                  navigateImage('prev');
                }}
              >
                <span className="material-icons">arrow_back_ios</span>
              </button>

              <motion.div
                className="max-w-4xl max-h-screen p-4"
                initial={{ scale: 0.9 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0.9 }}
                onClick={(e) => e.stopPropagation()}
              >
                {visibleItems[currentImage] && (
                  <>
                    <img
                      src={visibleItems[currentImage].image}
                      alt={visibleItems[currentImage].alt}
                      className="max-w-full max-h-[80vh] mx-auto"
                    />
                    <div className="text-center mt-4">
                      <p className="text-white text-lg">{visibleItems[currentImage].alt}</p>
                    </div>
                  </>
                )}
              </motion.div>

              <button
                type="button"
                className="absolute right-6 top-1/2 transform -translate-y-1/2 text-white text-4xl z-10"
                onClick={(e) => {
                  e.stopPropagation();
                  navigateImage('next');
                }}
              >
                <span className="material-icons">arrow_forward_ios</span>
              </button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
};

export default GaleriaSection;

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { apiRequest } from '@/lib/queryClient';

interface ContactSectionProps {
  onFormSuccess: () => void;
}

const ContactSection: React.FC<ContactSectionProps> = ({ onFormSuccess }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  
  const [formErrors, setFormErrors] = useState<Partial<typeof formData>>({});
  const [loading, setLoading] = useState(false);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field when user types
    if (formErrors[name as keyof typeof formData]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Form validation
  const validateForm = () => {
    const errors: Partial<typeof formData> = {};
    
    if (!formData.name.trim()) {
      errors.name = 'El nombre es requerido';
    }
    
    if (!formData.email.trim()) {
      errors.email = 'El correo electrónico es requerido';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Ingrese un correo electrónico válido';
    }
    
    if (!formData.subject.trim()) {
      errors.subject = 'El asunto es requerido';
    }
    
    if (!formData.message.trim()) {
      errors.message = 'El mensaje es requerido';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setLoading(true);
    
    try {
      await apiRequest('POST', '/api/contact', formData);
      
      // Reset form
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
      
      // Call success callback
      onFormSuccess();
    } catch (error) {
      console.error('Error submitting contact form:', error);
      
      // Handle error (could show toast message here)
    } finally {
      setLoading(false);
    }
  };

  return (
    <section id="contacto" className="py-20 bg-woodBrown-dark relative">
      {/* Background texture */}
      <div 
        className="absolute inset-0 opacity-10 pointer-events-none bg-grain"
      ></div>

      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <motion.h2 
            className="text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-white mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5 }}
          >
            Contacto
          </motion.h2>
          <motion.p 
            className="text-lg text-gray-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Estamos a tu disposición para resolver cualquier duda sobre nuestra cabaña.
          </motion.p>
        </div>
        
        <div className="flex flex-col lg:flex-row gap-12">
          {/* Contact Form */}
          <motion.div 
            className="lg:w-2/3 bg-white rounded-lg shadow-premium p-8"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6 }}
          >
            <h3 className="text-2xl font-serif font-bold text-woodBrown-dark mb-6">Envíanos un Mensaje</h3>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-stoneGray-dark mb-2">
                    Nombre Completo
                  </label>
                  <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    className={`w-full px-4 py-3 border ${formErrors.name ? 'border-red-500' : 'border-stoneGray-light'} rounded-sm focus:outline-none focus:ring-2 focus:ring-amberGold-light focus:border-transparent transition-all`}
                    placeholder="Tu nombre completo"
                    value={formData.name}
                    onChange={handleInputChange}
                  />
                  {formErrors.name && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.name}</p>
                  )}
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-stoneGray-dark mb-2">
                    Correo Electrónico
                  </label>
                  <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    className={`w-full px-4 py-3 border ${formErrors.email ? 'border-red-500' : 'border-stoneGray-light'} rounded-sm focus:outline-none focus:ring-2 focus:ring-amberGold-light focus:border-transparent transition-all`}
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={handleInputChange}
                  />
                  {formErrors.email && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.email}</p>
                  )}
                </div>
              </div>
              
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-stoneGray-dark mb-2">
                  Teléfono
                </label>
                <input 
                  type="tel" 
                  id="phone" 
                  name="phone" 
                  className="w-full px-4 py-3 border border-stoneGray-light rounded-sm focus:outline-none focus:ring-2 focus:ring-amberGold-light focus:border-transparent transition-all"
                  placeholder="+56 9 1234 5678"
                  value={formData.phone}
                  onChange={handleInputChange}
                />
              </div>
              
              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-stoneGray-dark mb-2">
                  Asunto
                </label>
                <input 
                  type="text" 
                  id="subject" 
                  name="subject" 
                  className={`w-full px-4 py-3 border ${formErrors.subject ? 'border-red-500' : 'border-stoneGray-light'} rounded-sm focus:outline-none focus:ring-2 focus:ring-amberGold-light focus:border-transparent transition-all`}
                  placeholder="Asunto de tu mensaje"
                  value={formData.subject}
                  onChange={handleInputChange}
                />
                {formErrors.subject && (
                  <p className="mt-1 text-sm text-red-500">{formErrors.subject}</p>
                )}
              </div>
              
              <div>
                <label htmlFor="message" className="block text-sm font-medium text-stoneGray-dark mb-2">
                  Mensaje
                </label>
                <textarea 
                  id="message" 
                  name="message" 
                  rows={5} 
                  className={`w-full px-4 py-3 border ${formErrors.message ? 'border-red-500' : 'border-stoneGray-light'} rounded-sm focus:outline-none focus:ring-2 focus:ring-amberGold-light focus:border-transparent transition-all resize-none`}
                  placeholder="Escribe tu mensaje aquí..."
                  value={formData.message}
                  onChange={handleInputChange}
                ></textarea>
                {formErrors.message && (
                  <p className="mt-1 text-sm text-red-500">{formErrors.message}</p>
                )}
              </div>
              
              <div>
                <button 
                  type="submit" 
                  className="w-full px-6 py-4 bg-woodBrown hover:bg-woodBrown-dark text-white rounded-sm shadow-lg transition-all transform hover:-translate-y-0.5 font-medium flex items-center justify-center"
                  disabled={loading}
                >
                  {loading ? (
                    <span className="inline-block animate-spin mr-2 material-icons">
                      autorenew
                    </span>
                  ) : (
                    <>
                      <span>Enviar Mensaje</span>
                      <span className="material-icons ml-2">send</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </motion.div>
          
          {/* Contact Information */}
          <div className="lg:w-1/3">
            <motion.div 
              className="bg-white rounded-lg shadow-premium p-8 mb-8"
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.6 }}
            >
              <h3 className="text-xl font-serif font-bold text-woodBrown-dark mb-6">Información de Contacto</h3>
              
              <div className="space-y-4">
                <div className="flex items-start">
                  <span className="material-icons text-amberGold-dark mr-4">location_on</span>
                  <div>
                    <h4 className="font-medium text-woodBrown-dark">Dirección</h4>
                    <p className="text-sm text-stoneGray-dark">Sector La Banda, Lote 9, Sitio 13, Pica, Chile</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="material-icons text-amberGold-dark mr-4">phone</span>
                  <div>
                    <h4 className="font-medium text-woodBrown-dark">Teléfono</h4>
                    <p className="text-sm text-stoneGray-dark">+56 9 1234 5678</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="material-icons text-amberGold-dark mr-4">email</span>
                  <div>
                    <h4 className="font-medium text-woodBrown-dark">Email</h4>
                    <p className="text-sm text-stoneGray-dark"><EMAIL></p>
                  </div>
                </div>
              </div>
            </motion.div>
            
            <motion.div 
              className="bg-amberGold bg-opacity-20 rounded-lg p-8"
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <h3 className="text-xl font-serif font-bold text-woodBrown-dark mb-4">Horario de Atención</h3>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">Lunes - Viernes:</span>
                  <span>9:00 - 20:00</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="font-medium">Sábado:</span>
                  <span>10:00 - 18:00</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="font-medium">Domingo:</span>
                  <span>10:00 - 16:00</span>
                </div>
              </div>
              
              <div className="mt-6 pt-4 border-t border-amberGold-dark border-opacity-20">
                <h4 className="font-medium text-woodBrown-dark mb-2">Emergencias:</h4>
                <p className="text-sm">Para consultas urgentes fuera de horario, por favor contáctanos por WhatsApp al +56 9 1234 5678.</p>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;

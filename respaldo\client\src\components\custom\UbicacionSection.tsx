import React from 'react';
import { motion } from 'framer-motion';

interface AttractionCardProps {
  image: string;
  title: string;
  description: string;
  distance: string;
  index: number;
}

const AttractionCard: React.FC<AttractionCardProps> = ({ image, title, description, distance, index }) => {
  return (
    <motion.div 
      className="bg-white rounded-lg shadow-xl overflow-hidden group"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
    >
      <div className="h-56 overflow-hidden">
        <img 
          src={image} 
          alt={title} 
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
        />
      </div>
      
      <div className="p-6">
        <h4 className="font-serif font-bold text-woodBrown-dark text-xl mb-2">{title}</h4>
        
        <p className="text-sm text-stoneGray-dark mb-4">
          {description}
        </p>
        
        <div className="flex items-center justify-between">
          <span className="text-amber-600 font-medium text-sm">{distance}</span>
          
          <a href="#" className="text-forestGreen hover:text-forestGreen-dark transition-colors flex items-center">
            <span className="text-sm font-medium">Ver más</span>
            <span className="material-icons text-sm ml-1">arrow_forward</span>
          </a>
        </div>
      </div>
    </motion.div>
  );
};

const UbicacionSection = () => {
  const attractions = [
    {
      image: "C:\Users\<USER>\Documents\Proyectos\Web\Posada2.0\client\src\assets\atracciones\cocha-resbaladero\Cocha_Resbaladero.png",
      title: "Cocha Resbaladero",
      description: "Reconocidas piscinas naturales de aguas templadas, ideales para refrescarse y relajarse. Un oasis en el desierto con propiedades terapéuticas.",
      distance: "A 10 minutos"
    },
    {
      image: "https://pixabay.com/get/g06082c86505017215e134e314fbfd737d3c567b865e19ebfe6c120de62a968df6d030966836c8f5dfb1523a75ec8c60d8087de31f36b44ff16f71a45f4393a10_1280.jpg",
      title: "Pueblo de Matilla",
      description: "Encantador pueblo histórico conocido por sus iglesias coloniales, viñedos tradicionales y la producción del famoso vino de Matilla.",
      distance: "A 15 minutos"
    },
    {
      image: "https://pixabay.com/get/g79721b392d6c80e8a12cdd35f40c7c181d16cc4c5c7cb9a27ad52e5cc1e3fa897bc46b6c6fc4ff5dd7c82efec86c86517528bae92eb9d34c2756ddd128b947c8_1280.jpg",
      title: "Salar de Huasco",
      description: "Impresionante salar ubicado en medio de la cordillera, hogar de flamencos, vicuñas y otras especies. Un paraíso para los amantes de la fotografía.",
      distance: "A 1 hora"
    },
    {
      image: "https://pixabay.com/get/g9504e2cab0e5498ec0cb2df1e3f764cf85ca8b9fa75cf657a1249f2c434fe653fb55570454709f445fb9d5cca3eb51a530a29f104de8fa7070fe55719ccafce6_1280.jpg",
      title: "Plantaciones de Limón de Pica",
      description: "Visita las famosas plantaciones del limón de Pica, producto emblemático de la región conocido por su intenso aroma y sabor.",
      distance: "A 5 minutos"
    },
    {
      image: "https://images.unsplash.com/photo-1504618223053-559bdef9dd5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      title: "Geoglifos Valle del Inca",
      description: "Impresionantes figuras grabadas en las laderas de los cerros, testimonio de las antiguas culturas que habitaron la zona.",
      distance: "A 30 minutos"
    },
    {
      image: "https://pixabay.com/get/g11f3f81f74d58dacbfe40272044b4100217a3d7ff9dc1a44568902dbcc9e0505c12234939ad9750a791fbf5f8e470df5860eb1473d9b5a39922618275d47334e_1280.jpg",
      title: "Reserva Pampa del Tamarugal",
      description: "Bosque único de tamarugos en pleno desierto, hogar de diversas especies y un fenómeno natural sorprendente.",
      distance: "A 45 minutos"
    }
  ];

  return (
    <section id="ubicacion" className="py-20 bg-stoneGray-dark relative">
      {/* Background texture */}
      <div 
        className="texture-background ubicacion-background-texture" 
      />

      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <motion.h2 
            className="text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-white mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6 }}
          >
            UBICACIÓN Y ACTIVIDADES
          </motion.h2>
          <motion.p 
            className="text-lg text-gray-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Descubre la belleza natural y cultural que rodea nuestra cabaña en Pica, un oasis en el desierto más árido del mundo.
          </motion.p>
        </div>
        
        {/* Map Section */}
        <motion.div 
          className="bg-white rounded-lg shadow-xl overflow-hidden mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex flex-col md:flex-row">
            <div className="md:w-1/2 p-8">
              <h3 className="text-2xl font-serif font-bold text-woodBrown-dark mb-4">Ubicación Privilegiada</h3>
              
              <p className="mb-6 text-stoneGray-dark">
                Nuestra cabaña está estratégicamente ubicada en Sector La Banda, Lote 9, Sitio 13, Pica, ofreciendo un perfecto balance entre tranquilidad y accesibilidad a las atracciones locales.
              </p>
              
              <div className="mb-6">
                <h4 className="font-medium text-woodBrown-dark mb-2">Cómo Llegar:</h4>
                
                <div className="flex items-start mb-4">
                  <span className="material-icons text-forestGreen mr-2 mt-1">directions_car</span>
                  <p className="text-sm text-stoneGray-dark">
                    Desde Iquique: Tomar la Ruta 1 hacia el sur, conectar con la Ruta 15 y seguir las indicaciones hacia Pica. Aproximadamente 1 hora y 45 minutos (117 km).
                  </p>
                </div>
                
                <div className="flex items-start">
                  <span className="material-icons text-forestGreen mr-2 mt-1">flight</span>
                  <p className="text-sm text-stoneGray-dark">
                    El aeropuerto más cercano es el Aeropuerto Internacional Diego Aracena (IQQ) en Iquique, a 120 km de distancia.
                  </p>
                </div>
              </div>
              
              <a 
                href="https://maps.google.com/?q=-20.4900,-69.3300" 
                target="_blank" 
                rel="noopener noreferrer"
                className="inline-flex items-center px-6 py-3 bg-forestGreen hover:bg-forestGreen-dark text-white rounded-sm shadow transition-all font-medium"
              >
                <span className="material-icons mr-2">map</span>
                Abrir en Google Maps
              </a>
            </div>
            
            <div className="md:w-1/2 h-80 md:h-auto">
              <iframe 
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d14844.57467911693!2d-69.33959719208985!3d-20.489999999999995!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x915247a6bc0fce1f%3A0x5e5f64e37c3eb70a!2sPica%2C%20Tarapac%C3%A1%2C%20Chile!5e0!3m2!1ses!2s!4v1628185012345!5m2!1ses!2s" 
                width="100%" 
                height="100%" 
                style={{ border: 0 }} 
                allowFullScreen={true} 
                loading="lazy" 
                referrerPolicy="no-referrer-when-downgrade"
                title="Ubicación de la cabaña en Pica, Chile"
              ></iframe>
            </div>
          </div>
        </motion.div>
        
        {/* Attractions */}
        <motion.h3 
          className="text-2xl md:text-3xl font-serif font-bold text-white mb-8 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          Descubre los Alrededores
        </motion.h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {attractions.map((attraction, index) => (
            <AttractionCard 
              key={index}
              {...attraction}
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default UbicacionSection;



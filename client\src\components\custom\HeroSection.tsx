import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import AnimatedText from '@/components/common/AnimatedText';

const HeroSection = () => {
  const heroRef = useRef<HTMLDivElement>(null);

  // Scroll to next section on arrow click
  const scrollToNextSection = () => {
    const nextSection = document.getElementById('cabana');
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Subtle parallax effect
  useEffect(() => {
    const handleScroll = () => {
      if (!heroRef.current) return;

      const scrollY = window.scrollY;
      if (scrollY < window.innerHeight) {
        const bgPosition = scrollY * 0.5;
        heroRef.current.style.backgroundPosition = `center calc(50% + ${bgPosition}px)`;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <section
      id="inicio"
      ref={heroRef}
      className="relative h-screen overflow-hidden flex items-center bg-cover bg-center hero-background hero-background-piscina"
    >
      {/* Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-black opacity-50"></div>

      <div className="relative container mx-auto px-4 h-full flex flex-col justify-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
          className="max-w-3xl"
        >
          <AnimatedText
            text="LA POSADA DEL OSO"
            className="text-4xl md:text-6xl lg:text-7xl font-rustic font-bold text-white leading-tight uppercase tracking-wide text-shadow-dark"
            tag="h1"
            delay={800}
          />

          <AnimatedText
            text="CONFORT EN LA NATURALEZA"
            className="text-4xl md:text-6xl lg:text-7xl font-rustic font-bold text-amber-400 mb-4 leading-tight uppercase tracking-wide"
            tag="span"
            delay={1200}
          />

          <motion.p
            className="text-xl md:text-2xl text-gray-200 mb-8 max-w-2xl text-shadow-dark font-rustic"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 1.5 }}
          >
            Una exclusiva cabaña en Pica, Chile donde el confort y la exclusividad se fusionan con la belleza natural del bosque.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 1.8 }}
          >
            <a
              href="#cabana"
              onClick={(e) => {
                e.preventDefault();
                document.getElementById('cabana')?.scrollIntoView({ behavior: 'smooth' });
              }}
              className="px-8 py-4 bg-woodBrown hover:bg-woodBrown-dark text-white text-lg rounded-full shadow-premium transition-all transform hover:-translate-y-0.5 hover:shadow-glow font-rustic font-bold uppercase tracking-wide text-shadow text-center"
            >
              Descubrir Nuestra Cabaña
            </a>
            <a
              href="#reservar"
              onClick={(e) => {
                e.preventDefault();
                document.getElementById('reservar')?.scrollIntoView({ behavior: 'smooth' });
              }}
              className="px-8 py-4 bg-amber-500 hover:bg-amber-400 text-woodBrown-dark text-lg rounded-full shadow-premium transition-all transform hover:-translate-y-0.5 hover:shadow-glow font-rustic font-bold uppercase tracking-wide text-center"
            >
              Reservar Ahora
            </a>
          </motion.div>
        </motion.div>

        <motion.div
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 1.5, repeat: Infinity, repeatType: "reverse" }}
        >
          <a
            href="#cabana"
            onClick={(e) => {
              e.preventDefault();
              scrollToNextSection();
            }}
            className="text-white opacity-80 hover:opacity-100 transition-opacity block"
          >
            <span className="material-icons text-4xl">keyboard_arrow_down</span>
          </a>
        </motion.div>
      </div>
    </section>
  );
};

export default HeroSection;

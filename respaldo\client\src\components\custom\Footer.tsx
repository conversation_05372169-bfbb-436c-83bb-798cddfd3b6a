import React from 'react';
import { motion } from 'framer-motion';

const Footer: React.FC = () => {
  // Scroll to section function
  const scrollToSection = (sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="bg-woodBrown-dark text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <a 
              href="#inicio" 
              className="inline-block mb-4 flex items-center"
              onClick={(e) => {
                e.preventDefault();
                scrollToSection('inicio');
              }}
            >
              <div className="h-10 w-10 mr-3 bg-amberGold-light rounded-full flex items-center justify-center">
                <span className="material-icons text-woodBrown-dark">pets</span>
              </div>
              <span className="text-2xl font-serif font-bold">
                <span className="text-amberGold-light">La Posada</span> del Oso
              </span>
            </a>
            
            <p className="text-gray-400 mb-4">
              Una experiencia de lujo rústico en medio de la naturaleza de Pica, Chile.
            </p>
            
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-amberGold-light transition-colors">
                <span className="material-icons">facebook</span>
              </a>
              <a href="#" className="text-gray-400 hover:text-amberGold-light transition-colors">
                <span className="material-icons">photo_camera</span>
              </a>
              <a href="#" className="text-gray-400 hover:text-amberGold-light transition-colors">
                <span className="material-icons">whatsapp</span>
              </a>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <h3 className="text-lg font-medium mb-4">Enlaces Rápidos</h3>
            
            <ul className="space-y-2">
              {['inicio', 'cabana', 'entorno', 'galeria', 'contacto', 'reservar'].map(section => (
                <li key={section}>
                  <a 
                    href={`#${section}`}
                    className="text-gray-400 hover:text-amberGold-light transition-colors"
                    onClick={(e) => {
                      e.preventDefault();
                      scrollToSection(section);
                    }}
                  >
                    {section.charAt(0).toUpperCase() + section.slice(1)}
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h3 className="text-lg font-medium mb-4">Contacto</h3>
            
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="material-icons text-amberGold-light mr-2">location_on</span>
                <span className="text-gray-400">Sector La Banda, Lote 9, Sitio 13, Pica, Chile</span>
              </li>
              <li className="flex items-start">
                <span className="material-icons text-amberGold-light mr-2">phone</span>
                <span className="text-gray-400">+56 9 1234 5678</span>
              </li>
              <li className="flex items-start">
                <span className="material-icons text-amberGold-light mr-2">email</span>
                <span className="text-gray-400"><EMAIL></span>
              </li>
            </ul>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <h3 className="text-lg font-medium mb-4">Boletín Informativo</h3>
            
            <p className="text-gray-400 mb-4">
              Suscríbete para recibir ofertas exclusivas y novedades.
            </p>
            
            <form 
              className="flex" 
              onSubmit={(e) => {
                e.preventDefault();
                // Here you would handle the newsletter signup
                // For now just clear the input
                const input = e.currentTarget.querySelector('input');
                if (input) input.value = '';
                // Could show a toast message
              }}
            >
              <input 
                type="email" 
                placeholder="Tu correo electrónico" 
                className="px-4 py-2 bg-woodBrown border border-stoneGray-light rounded-l-sm text-white focus:outline-none flex-grow"
                required
              />
              <button 
                type="submit" 
                className="px-4 py-2 bg-amberGold hover:bg-amberGold-light text-woodBrown-dark rounded-r-sm transition-colors"
              >
                <span className="material-icons">send</span>
              </button>
            </form>
          </motion.div>
        </div>
        
        <div className="border-t border-stoneGray-dark mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            &copy; {new Date().getFullYear()} Cabaña Premium. Todos los derechos reservados.
          </p>
          
          <div className="flex space-x-4 mt-4 md:mt-0">
            <a href="#" className="text-gray-400 hover:text-amberGold-light transition-colors text-sm">
              Política de Privacidad
            </a>
            <a href="#" className="text-gray-400 hover:text-amberGold-light transition-colors text-sm">
              Términos y Condiciones
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

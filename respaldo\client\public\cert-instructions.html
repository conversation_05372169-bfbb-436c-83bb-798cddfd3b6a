<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instrucciones para Certificado SSL</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .browser {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h2 {
            color: #3498db;
        }
        img {
            max-width: 100%;
            height: auto;
            margin: 15px 0;
            border: 1px solid #ddd;
        }
        .note {
            background-color: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .warning {
            background-color: #f8d7da;
            padding: 10px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
        .btn {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            font-size: 16px;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Instrucciones para Aceptar el Certificado SSL</h1>
    
    <div class="note">
        <p><strong>Nota:</strong> Estás viendo esta página porque estás utilizando un certificado SSL autofirmado en un entorno de desarrollo. En un entorno de producción, se utilizaría un certificado emitido por una autoridad de certificación confiable.</p>
    </div>
    
    <div class="warning">
        <p><strong>Advertencia:</strong> Solo debes aceptar certificados autofirmados en entornos de desarrollo que controles. Nunca aceptes certificados no confiables en sitios web públicos.</p>
    </div>
    
    <h2>¿Por qué veo una advertencia de seguridad?</h2>
    <p>Tu navegador muestra una advertencia porque el certificado SSL utilizado por este sitio es "autofirmado", lo que significa que no ha sido validado por una autoridad de certificación confiable. Esto es normal en entornos de desarrollo.</p>
    
    <div class="browser">
        <h2>Chrome</h2>
        <ol>
            <li>Cuando veas la advertencia "Tu conexión no es privada", haz clic en "Avanzado".</li>
            <li>Luego, haz clic en "Continuar a localhost (no seguro)".</li>
        </ol>
    </div>
    
    <div class="browser">
        <h2>Firefox</h2>
        <ol>
            <li>Cuando veas la advertencia "Advertencia: Riesgo potencial de seguridad", haz clic en "Avanzado".</li>
            <li>Luego, haz clic en "Aceptar el riesgo y continuar".</li>
        </ol>
    </div>
    
    <div class="browser">
        <h2>Edge</h2>
        <ol>
            <li>Cuando veas la advertencia "Tu conexión no es privada", haz clic en "Avanzado".</li>
            <li>Luego, haz clic en "Continuar a localhost (no seguro)".</li>
        </ol>
    </div>
    
    <div class="browser">
        <h2>Safari</h2>
        <ol>
            <li>Cuando veas la advertencia "Esta conexión no es privada", haz clic en "Mostrar detalles".</li>
            <li>Luego, haz clic en "Visitar este sitio web".</li>
            <li>Confirma haciendo clic en "Visitar sitio web" en el cuadro de diálogo.</li>
        </ol>
    </div>
    
    <a href="https://localhost:5443" class="btn">Volver a intentar acceder al sitio</a>
</body>
</html>

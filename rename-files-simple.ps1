# Definir la ruta base
$basePath = "client\src\assets\atracciones"

# Renombrar archivos en cocha-resbaladero
$folder = "cocha-resbaladero"
$folderPath = Join-Path -Path $basePath -ChildPath $folder

if (Test-Path (Join-Path -Path $folderPath -ChildPath "Cocha1.webp")) {
    Rename-Item -Path (Join-Path -Path $folderPath -ChildPath "Cocha1.webp") -NewName "cocha-resbaladero-1.webp"
    Write-Host "Archivo 'Cocha1.webp' renombrado a 'cocha-resbaladero-1.webp'" -ForegroundColor Green
}

if (Test-Path (Join-Path -Path $folderPath -ChildPath "Cocha2.webp")) {
    Rename-Item -Path (Join-Path -Path $folderPath -ChildPath "Cocha2.webp") -NewName "cocha-resbaladero-2.webp"
    Write-Host "Archivo 'Cocha2.webp' renombrado a 'cocha-resbaladero-2.webp'" -ForegroundColor Green
}

if (Test-Path (Join-Path -Path $folderPath -ChildPath "Cocha3.webp")) {
    Rename-Item -Path (Join-Path -Path $folderPath -ChildPath "Cocha3.webp") -NewName "cocha-resbaladero-3.webp"
    Write-Host "Archivo 'Cocha3.webp' renombrado a 'cocha-resbaladero-3.webp'" -ForegroundColor Green
}

if (Test-Path (Join-Path -Path $folderPath -ChildPath "Cocha4.webp")) {
    Rename-Item -Path (Join-Path -Path $folderPath -ChildPath "Cocha4.webp") -NewName "cocha-resbaladero-4.webp"
    Write-Host "Archivo 'Cocha4.webp' renombrado a 'cocha-resbaladero-4.webp'" -ForegroundColor Green
}

# Renombrar archivos en iglesia-san-andres
$folder = "iglesia-san-andres"
$folderPath = Join-Path -Path $basePath -ChildPath $folder

if (Test-Path (Join-Path -Path $folderPath -ChildPath "Iglesia_San_Andres_Pica1.webp")) {
    Rename-Item -Path (Join-Path -Path $folderPath -ChildPath "Iglesia_San_Andres_Pica1.webp") -NewName "iglesia-san-andres-1.webp"
    Write-Host "Archivo 'Iglesia_San_Andres_Pica1.webp' renombrado a 'iglesia-san-andres-1.webp'" -ForegroundColor Green
}

if (Test-Path (Join-Path -Path $folderPath -ChildPath "Iglesia_San_Andres_Pica2.webp")) {
    Rename-Item -Path (Join-Path -Path $folderPath -ChildPath "Iglesia_San_Andres_Pica2.webp") -NewName "iglesia-san-andres-2.webp"
    Write-Host "Archivo 'Iglesia_San_Andres_Pica2.webp' renombrado a 'iglesia-san-andres-2.webp'" -ForegroundColor Green
}

if (Test-Path (Join-Path -Path $folderPath -ChildPath "Iglesia_San_Andres_Pica3.webp")) {
    Rename-Item -Path (Join-Path -Path $folderPath -ChildPath "Iglesia_San_Andres_Pica3.webp") -NewName "iglesia-san-andres-3.webp"
    Write-Host "Archivo 'Iglesia_San_Andres_Pica3.webp' renombrado a 'iglesia-san-andres-3.webp'" -ForegroundColor Green
}

# Renombrar archivos en parque-dinosaurios
$folder = "parque-dinosaurios"
$folderPath = Join-Path -Path $basePath -ChildPath $folder

if (Test-Path (Join-Path -Path $folderPath -ChildPath "Parque_dinosaurios1.webp")) {
    Rename-Item -Path (Join-Path -Path $folderPath -ChildPath "Parque_dinosaurios1.webp") -NewName "parque-dinosaurios-1.webp"
    Write-Host "Archivo 'Parque_dinosaurios1.webp' renombrado a 'parque-dinosaurios-1.webp'" -ForegroundColor Green
}

if (Test-Path (Join-Path -Path $folderPath -ChildPath "Parque_dinosaurios2.webp")) {
    Rename-Item -Path (Join-Path -Path $folderPath -ChildPath "Parque_dinosaurios2.webp") -NewName "parque-dinosaurios-2.webp"
    Write-Host "Archivo 'Parque_dinosaurios2.webp' renombrado a 'parque-dinosaurios-2.webp'" -ForegroundColor Green
}

if (Test-Path (Join-Path -Path $folderPath -ChildPath "Parque_dinosaurios3.webp")) {
    Rename-Item -Path (Join-Path -Path $folderPath -ChildPath "Parque_dinosaurios3.webp") -NewName "parque-dinosaurios-3.webp"
    Write-Host "Archivo 'Parque_dinosaurios3.webp' renombrado a 'parque-dinosaurios-3.webp'" -ForegroundColor Green
}

Write-Host "Proceso completado." -ForegroundColor Cyan

import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import fs from "fs";
import path from "path";
import https from "https";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Middleware para redirigir HTTP a HTTPS en producción
app.use((req, res, next) => {
  if (app.get('env') === 'production' && !req.secure) {
    // Verificar si la solicitud es segura (HTTPS)
    // En algunos entornos, como detrás de un proxy, req.secure podría no funcionar correctamente
    // En ese caso, puedes verificar el encabezado X-Forwarded-Proto
    const isSecure = req.secure || req.headers['x-forwarded-proto'] === 'https';

    if (!isSecure && req.method === 'GET') {
      // En desarrollo, redirigir a la página de ayuda para certificados
      if (app.get('env') === 'development' && req.url !== '/cert-help') {
        // Redirigir a HTTPS (usando el puerto 5443)
        const host = req.headers.host?.split(':')[0] || 'localhost';
        return res.redirect(301, `https://${host}:5443/cert-help`);
      } else {
        // Redirigir a HTTPS (usando el puerto 5443)
        const host = req.headers.host?.split(':')[0] || 'localhost';
        return res.redirect(301, `https://${host}:5443${req.url}`);
      }
    }
  }
  next();
});

// Middleware para agregar cabeceras de seguridad
app.use((req, res, next) => {
  // Strict-Transport-Security: Forzar HTTPS en todos los navegadores compatibles
  // max-age: tiempo en segundos que el navegador recordará que el sitio solo debe accederse usando HTTPS (2 años)
  res.setHeader('Strict-Transport-Security', 'max-age=63072000; includeSubDomains; preload');

  // X-Content-Type-Options: Evitar que el navegador intente MIME-sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');

  // X-Frame-Options: Evitar clickjacking
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');

  // X-XSS-Protection: Protección XSS en navegadores antiguos
  res.setHeader('X-XSS-Protection', '1; mode=block');

  // Content-Security-Policy: Política más permisiva para desarrollo
  if (app.get('env') === 'development') {
    // En desarrollo, usamos una política más permisiva
    res.setHeader(
      'Content-Security-Policy',
      "default-src * 'unsafe-inline' 'unsafe-eval'; script-src * 'unsafe-inline' 'unsafe-eval'; connect-src * 'unsafe-inline'; img-src * data: blob: 'unsafe-inline'; frame-src *; style-src * 'unsafe-inline';"
    );
  } else {
    // En producción, usamos una política más restrictiva
    res.setHeader(
      'Content-Security-Policy',
      "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self'"
    );
  }

  // Referrer-Policy: Controlar la información del referrer
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  next();
});

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Configuración para HTTPS
  const httpsOptions = {
    key: fs.readFileSync(path.join(process.cwd(), 'certs/key.pem')),
    cert: fs.readFileSync(path.join(process.cwd(), 'certs/cert.pem')),
    // En desarrollo, no rechazamos conexiones no autorizadas
    // Esto es útil para certificados autofirmados
    rejectUnauthorized: app.get('env') !== 'development'
  };

  // Puertos para los servidores
  const httpsPort = 5443; // Puerto estándar alternativo para HTTPS
  const httpPort = 5080;  // Puerto alternativo para HTTP

  // Crear servidor HTTPS
  const httpsServer = https.createServer(httpsOptions, app);

  httpsServer.listen(httpsPort, "0.0.0.0", () => {
    log(`Servidor HTTPS ejecutándose en https://localhost:${httpsPort}`);
  });

  // También mantenemos el servidor HTTP para redireccionar a HTTPS
  server.listen(httpPort, "0.0.0.0", () => {
    log(`Servidor HTTP ejecutándose en http://localhost:${httpPort} (redirecciona a HTTPS)`);
  });
})();

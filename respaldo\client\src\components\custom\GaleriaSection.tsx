import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

type GalleryCategory = 'all' | 'configuracion-2' | 'configuracion-3' | 'exteriores' | 'entorno';

interface GalleryItem {
  image: string;
  alt: string;
  category: Exclude<GalleryCategory, 'all'>;
  height: 'h-56' | 'h-64' | 'h-80';
}

const GaleriaSection = () => {
  const [activeFilter, setActiveFilter] = useState<GalleryCategory>('all');
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImage, setCurrentImage] = useState<number>(0);
  const [visibleItems, setVisibleItems] = useState<GalleryItem[]>([]);

  const galleryItems: GalleryItem[] = [
    {
      image: "https://images.unsplash.com/photo-1542718610-a1d656d1884c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      alt: "Vista exterior de la cabaña durante el día",
      category: "exteriores",
      height: "h-64"
    },
    {
      image: "https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      alt: "Dormitorio principal en configuración de 2 dormitorios",
      category: "configuracion-2",
      height: "h-80"
    },
    {
      image: "https://images.unsplash.com/photo-1602343168117-bb8ffe3e2e9f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80", 
      alt: "Piscina privada de la cabaña",
      category: "exteriores",
      height: "h-56"
    },
    {
      image: "https://images.unsplash.com/photo-1584622781564-1d987f7333c1?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      alt: "Tercer dormitorio en configuración de 3 dormitorios",
      category: "configuracion-3",
      height: "h-64"
    },
    {
      image: "https://pixabay.com/get/gba71616c57ec2c153f4344c43a548117b6b60c99cc9f1c035122525ad74dde3ace1c8bd7259f01a902dbc0c14dc69fd38b78a3e9f642da328602cd47dbecc657_1280.jpg",
      alt: "Paisaje de los alrededores de Pica al atardecer",
      category: "entorno",
      height: "h-80"
    },
    {
      image: "https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      alt: "Área de quincho al aire libre",
      category: "exteriores",
      height: "h-56"
    },
    {
      image: "https://images.unsplash.com/photo-1488750059241-ed3ad4563245?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      alt: "Sala de estar de la cabaña",
      category: "configuracion-2",
      height: "h-64"
    },
    {
      image: "https://pixabay.com/get/ga9e8c86452390c84d48adee965aebb43db57ec7bc87bd2400ce08e0e59e104a5a7680f0a4bef31ced403966fa35b4929d50597d8e0f3cdd15f5c4288800de8b5_1280.jpg",
      alt: "Los famosos limones de Pica",
      category: "entorno",
      height: "h-56"
    },
    {
      image: "https://images.unsplash.com/photo-1519690889869-e705e59f72e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      alt: "Comedor en configuración de 3 dormitorios",
      category: "configuracion-3",
      height: "h-80"
    },
    {
      image: "https://images.unsplash.com/photo-1618767689160-da3fb810aad7?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      alt: "Cabaña de noche con iluminación",
      category: "exteriores",
      height: "h-64"
    },
    {
      image: "https://images.unsplash.com/photo-1604537529428-15bcbeecfe4d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
      alt: "Vista panorámica del desierto de Atacama cerca de Pica",
      category: "entorno",
      height: "h-56"
    },
    {
      image: "https://pixabay.com/get/ga159dc6d3f7a195ea5b5f08ee344f261b7574743aff1c5ed8d83c5bc7ef915cd107a6b96f9d19926cec43214d1d9222c272691236871b8c6a0abdb60b2d4ab51_1280.jpg",
      alt: "Área de cocina completamente equipada",
      category: "configuracion-2",
      height: "h-64"
    }
  ];

  // Filter items based on active filter
  useEffect(() => {
    if (activeFilter === 'all') {
      setVisibleItems(galleryItems);
    } else {
      setVisibleItems(galleryItems.filter(item => item.category === activeFilter));
    }
  }, [activeFilter]);

  const handleFilterClick = (filter: GalleryCategory) => {
    setActiveFilter(filter);
  };

  const openLightbox = (index: number) => {
    document.body.style.overflow = 'hidden';
    setCurrentImage(index);
    setLightboxOpen(true);
  };

  const closeLightbox = () => {
    document.body.style.overflow = '';
    setLightboxOpen(false);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setCurrentImage(prev => (prev === 0 ? visibleItems.length - 1 : prev - 1));
    } else {
      setCurrentImage(prev => (prev === visibleItems.length - 1 ? 0 : prev + 1));
    }
  };

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!lightboxOpen) return;
      
      if (e.key === 'Escape') {
        closeLightbox();
      } else if (e.key === 'ArrowLeft') {
        navigateImage('prev');
      } else if (e.key === 'ArrowRight') {
        navigateImage('next');
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [lightboxOpen]);

  return (
    <section id="galeria" className="py-20 bg-linen relative">
      {/* Background texture */}
      <div 
        className="absolute inset-0 opacity-10 pointer-events-none" 
        style={{ 
          backgroundImage: `url("data:image/png;base64,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")` 
        }}
      ></div>

      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <motion.h2 
            className="text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-woodBrown-dark mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5 }}
          >
            Galería
          </motion.h2>
          <motion.p 
            className="text-lg text-stoneGray-dark max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Explora nuestra cabaña a través de imágenes que capturan su esencia y belleza.
          </motion.p>
        </div>
        
        {/* Gallery Filters */}
        <motion.div 
          className="flex flex-wrap justify-center mb-10 gap-2"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          {[
            { id: 'all', label: 'Todas' },
            { id: 'configuracion-2', label: 'Configuración 2 Dormitorios' },
            { id: 'configuracion-3', label: 'Configuración 3 Dormitorios' },
            { id: 'exteriores', label: 'Exteriores' },
            { id: 'entorno', label: 'Entorno' }
          ].map(filter => (
            <button 
              key={filter.id}
              className={`px-4 py-2 rounded-sm transition-colors ${
                activeFilter === filter.id 
                  ? 'bg-woodBrown text-white' 
                  : 'bg-white text-woodBrown hover:bg-stoneGray-light'
              }`}
              onClick={() => handleFilterClick(filter.id as GalleryCategory)}
            >
              {filter.label}
            </button>
          ))}
        </motion.div>
        
        {/* Masonry Gallery */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <AnimatePresence>
            {visibleItems.map((item, index) => (
              <motion.div 
                key={item.image}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.4 }}
                className="gallery-item"
              >
                <div 
                  className={`rounded-lg overflow-hidden shadow-xl ${item.height} cursor-pointer hover:shadow-2xl transition-shadow`}
                  onClick={() => openLightbox(index)}
                >
                  <img 
                    src={item.image} 
                    alt={item.alt} 
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
        
        {/* Lightbox */}
        <AnimatePresence>
          {lightboxOpen && (
            <motion.div 
              className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={closeLightbox}
            >
              <button className="absolute top-6 right-6 text-white text-4xl z-10" onClick={closeLightbox}>
                ×
              </button>
              
              <button 
                className="absolute left-6 top-1/2 transform -translate-y-1/2 text-white text-4xl z-10"
                onClick={(e) => {
                  e.stopPropagation();
                  navigateImage('prev');
                }}
              >
                <span className="material-icons">arrow_back_ios</span>
              </button>
              
              <motion.div 
                className="max-w-4xl max-h-screen p-4"
                initial={{ scale: 0.9 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0.9 }}
                onClick={(e) => e.stopPropagation()}
              >
                {visibleItems[currentImage] && (
                  <>
                    <img 
                      src={visibleItems[currentImage].image} 
                      alt={visibleItems[currentImage].alt} 
                      className="max-w-full max-h-[80vh] mx-auto"
                    />
                    <div className="text-center mt-4">
                      <p className="text-white text-lg">{visibleItems[currentImage].alt}</p>
                    </div>
                  </>
                )}
              </motion.div>
              
              <button 
                className="absolute right-6 top-1/2 transform -translate-y-1/2 text-white text-4xl z-10"
                onClick={(e) => {
                  e.stopPropagation();
                  navigateImage('next');
                }}
              >
                <span className="material-icons">arrow_forward_ios</span>
              </button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
};

export default GaleriaSection;

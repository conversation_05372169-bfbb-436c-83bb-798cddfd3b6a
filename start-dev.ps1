# Script simplificado para iniciar el servidor de desarrollo

Write-Host "=======================================================`n  INICIANDO SERVIDOR DE DESARROLLO`n=======================================================" -ForegroundColor Cyan

# Verificar si hay procesos de Node.js en ejecución
$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
if ($nodeProcesses) {
    Write-Host "Terminando procesos de Node.js existentes..." -ForegroundColor Yellow
    $nodeProcesses | ForEach-Object { 
        Write-Host "Terminando proceso Node.js (ID: $($_.Id))..." -ForegroundColor Yellow
        Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue 
    }
    # Esperar un momento para que los procesos se terminen
    Start-Sleep -Seconds 2
}

# Iniciar el servidor con npm run dev
Write-Host "Iniciando el servidor de desarrollo..." -ForegroundColor Green
npm run dev

Write-Host "=======================================================`n  PROCESO COMPLETADO`n=======================================================" -ForegroundColor Cyan

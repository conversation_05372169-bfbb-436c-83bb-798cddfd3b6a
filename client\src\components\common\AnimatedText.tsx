import React, { useEffect, useRef } from 'react';
import { motion, useAnimation, Variants } from 'framer-motion';

type AnimatedTextProps = {
  text: string;
  className?: string;
  once?: boolean;
  delay?: number;
  tag?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span';
  type?: 'words' | 'characters';
};

const wordVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.05,
      duration: 0.5,
      ease: [0.22, 1, 0.36, 1]
    }
  })
};

const charVariants: Variants = {
  hidden: { opacity: 0, y: 10 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.03,
      duration: 0.3,
      ease: [0.22, 1, 0.36, 1]
    }
  })
};

const AnimatedText: React.FC<AnimatedTextProps> = ({
  text,
  className = '',
  once = true,
  delay = 0,
  tag = 'p',
  type = 'words'
}) => {
  const controls = useAnimation();
  const ref = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    let observer: IntersectionObserver;
    
    const handleIntersect = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          controls.start('visible');
          if (once && observer) {
            observer.disconnect();
          }
        } else if (!once) {
          controls.start('hidden');
        }
      });
    };
    
    if (ref.current) {
      observer = new IntersectionObserver(handleIntersect, {
        threshold: 0.2
      });
      observer.observe(ref.current);
    }
    
    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  }, [controls, once]);
  
  useEffect(() => {
    if (delay > 0) {
      const timer = setTimeout(() => {
        controls.start('visible');
      }, delay);
      
      return () => {
        clearTimeout(timer);
      };
    }
  }, [controls, delay]);
  
  const renderContent = () => {
    if (type === 'words') {
      return (
        <motion.div ref={ref} className={`inline-block ${className}`}>
          {text.split(' ').map((word, i) => (
            <motion.span
              key={`word-${i}`}
              className="inline-block mr-[0.25em] whitespace-nowrap"
              custom={i}
              variants={wordVariants}
              initial="hidden"
              animate={controls}
            >
              {word}
            </motion.span>
          ))}
        </motion.div>
      );
    } else {
      return (
        <motion.div ref={ref} className={`inline-block ${className}`}>
          {text.split('').map((char, i) => (
            <motion.span
              key={`char-${i}`}
              className="inline-block"
              custom={i}
              variants={charVariants}
              initial="hidden"
              animate={controls}
            >
              {char === ' ' ? '\u00A0' : char}
            </motion.span>
          ))}
        </motion.div>
      );
    }
  };

  const TagComponent = tag;
  
  return (
    <TagComponent className={className}>
      {renderContent()}
    </TagComponent>
  );
};

export default AnimatedText;

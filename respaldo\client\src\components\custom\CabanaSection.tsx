import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Tab } from '@/components/ui/tabs';

type ModalidadType = 'modalidad-1' | 'modalidad-2';

const CabanaSection = () => {
  const [activeTab, setActiveTab] = useState<ModalidadType>('modalidad-1');

  const quickFactItems = [
    { icon: 'privacy_tip', text: '100% Privado' },
    { icon: 'photo_camera', text: 'Vistas Panorámicas' },
    { icon: 'local_fire_department', text: 'Quincho Exclusivo' },
    { icon: 'pool', text: 'Piscinas Privadas' },
  ];

  const equipmentItems = [
    {
      icon: 'live_tv',
      title: 'Televisores Premium',
      description: 'Televisores con TV cable HD en dormitorios y sala de estar.'
    },
    {
      icon: 'local_parking',
      title: 'Estacionamiento Privado',
      description: 'Amplio espacio para hasta 3 vehículos con total seguridad.'
    },
    {
      icon: 'outdoor_grill',
      title: 'Quincho Privado',
      description: 'Completamente equipado para asados y reuniones al aire libre.'
    },
    {
      icon: 'pool',
      title: 'Piscinas Exclusivas',
      description: 'Piscina para adultos y niños de uso exclusivo para los huéspedes.'
    },
    {
      icon: 'sports_volleyball',
      title: 'Cama Saltarina',
      description: 'Diversión garantizada para los más pequeños en un entorno seguro.'
    },
    {
      icon: 'sports_esports',
      title: 'Juegos Recreativos',
      description: 'Mesa de pool y TACA TACA para entretenimiento de todas las edades.'
    }
  ];

  return (
    <section id="cabana" className="py-20 bg-linen relative">
      {/* Background texture */}
      <div 
        className="absolute inset-0 opacity-10 pointer-events-none" 
        style={{ 
          backgroundImage: `url("data:image/png;base64,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")` 
        }}
      ></div>

      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <motion.h2 
            className="text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-woodBrown-dark mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5 }}
          >
            Descubre Nuestra Cabaña
          </motion.h2>
          <motion.p 
            className="text-lg md:text-xl text-stoneGray-dark max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Una sola y espaciosa cabaña diseñada para adaptarse a diferentes tamaños de grupos, ofreciendo siempre total privacidad y acceso exclusivo a todas las instalaciones.
          </motion.p>
        </div>
        
        <div className="flex flex-col lg:flex-row gap-8 mb-16">
          {/* Cabaña Introduction */}
          <motion.div 
            className="lg:w-1/2"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6 }}
          >
            <div className="rounded-lg overflow-hidden shadow-xl h-96 md:h-128">
              <img 
                src="https://images.unsplash.com/photo-1587061949409-02df41d5e562?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80" 
                alt="Interior de nuestra cabaña premium con elegantes detalles en madera" 
                className="w-full h-full object-cover transition-transform duration-700 hover:scale-105"
              />
            </div>
          </motion.div>
          
          <motion.div 
            className="lg:w-1/2 flex flex-col justify-center"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6 }}
          >
            <h3 className="text-2xl md:text-3xl font-serif font-bold text-woodBrown-dark mb-6">Flexibilidad y Confort a tu Medida</h3>
            
            <div className="prose prose-lg max-w-none">
              <p className="mb-6">
                Nuestra cabaña está diseñada para ofrecer una experiencia Premium, adaptándose perfectamente a las necesidades de cada grupo. Tanto para familias como para amigos, contamos con configuraciones que garantizan comodidad sin sacrificar privacidad.
              </p>
              
              <p className="text-forestGreen font-medium text-lg mb-6">
                Sin importar la modalidad que elijas, tendrás acceso EXCLUSIVO a toda la propiedad y sus instalaciones. La cabaña completa y sus terrenos son solo para ti durante tu estadía.
              </p>
            </div>
            
            <div className="mt-4 flex space-x-6">
              {quickFactItems.map((item, index) => (
                <div key={index} className="flex flex-col items-center">
                  <span className="material-icons text-amber-600 text-3xl mb-2">{item.icon}</span>
                  <span className="text-sm text-stoneGray-dark text-center">{item.text}</span>
                </div>
              ))}
            </div>
          </motion.div>
        </div>
        
        {/* Modalidades Tabs */}
        <motion.div 
          className="bg-white rounded-lg shadow-xl p-8 mb-16"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.2 }}
          transition={{ duration: 0.6 }}
        >
          <h3 className="text-2xl md:text-3xl font-serif font-bold text-woodBrown-dark mb-6 text-center">Opciones de Configuración</h3>
          
          <div className="flex flex-col md:flex-row border-b border-stoneGray-light">
            <button 
              className={`py-4 px-6 md:px-10 font-medium focus:outline-none transition-colors ${
                activeTab === 'modalidad-1' 
                  ? 'text-woodBrown-dark border-b-2 border-amber-500' 
                  : 'text-stoneGray-dark hover:text-woodBrown'
              }`}
              onClick={() => setActiveTab('modalidad-1')}
            >
              Opción Familiar: 2 Dormitorios
            </button>
            <button 
              className={`py-4 px-6 md:px-10 font-medium focus:outline-none transition-colors ${
                activeTab === 'modalidad-2' 
                  ? 'text-woodBrown-dark border-b-2 border-amber-500' 
                  : 'text-stoneGray-dark hover:text-woodBrown'
              }`}
              onClick={() => setActiveTab('modalidad-2')}
            >
              Opción Grupo Extendido: 3 Dormitorios
            </button>
          </div>
          
          {/* Tab Content */}
          <div className="mt-8">
            {/* Modalidad 1 */}
            {activeTab === 'modalidad-1' && (
              <div className="flex flex-col lg:flex-row gap-8">
                <div className="lg:w-1/2">
                  <div className="rounded-lg overflow-hidden shadow-lg h-80">
                    <img 
                      src="https://images.unsplash.com/photo-1611892440504-42a792e24d32?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                      alt="Dormitorio principal con cama king size y acabados premium" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div className="rounded-lg overflow-hidden shadow-lg h-48">
                      <img 
                        src="https://images.unsplash.com/photo-1560185127-6ed189bf02f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                        alt="Segundo dormitorio con camas gemelas" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    <div className="rounded-lg overflow-hidden shadow-lg h-48">
                      <img 
                        src="https://images.unsplash.com/photo-1584622650111-993a426fbf0a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                        alt="Baño completo con acabados premium" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="lg:w-1/2">
                  <h4 className="text-xl font-serif font-bold text-woodBrown-dark mb-4">Opción Familiar: Hasta 6 Huéspedes</h4>
                  
                  <p className="mb-6 text-stoneGray-dark">
                    Perfecta para familias o grupos pequeños, esta configuración ofrece 2 dormitorios totalmente equipados, manteniendo todas las comodidades y el acceso exclusivo a las instalaciones completas.
                  </p>
                  
                  <div className="mb-6">
                    <div className="flex items-center mb-2">
                      <span className="material-icons text-forestGreen mr-2">people</span>
                      <span className="font-medium">Capacidad máxima: 6 personas</span>
                    </div>
                    
                    <div className="flex items-center mb-2">
                      <span className="material-icons text-forestGreen mr-2">king_bed</span>
                      <span className="font-medium">2 Dormitorios</span>
                    </div>
                    
                    <div className="flex items-center">
                      <span className="material-icons text-forestGreen mr-2">payments</span>
                      <span className="font-medium">Precio: $90.000 CLP / Noche</span>
                    </div>
                  </div>
                  
                  <a 
                    href="#reservar"
                    onClick={(e) => {
                      e.preventDefault();
                      document.getElementById('reservar')?.scrollIntoView({ behavior: 'smooth' });
                    }}
                    className="inline-block px-6 py-3 bg-woodBrown hover:bg-woodBrown-dark text-white rounded-sm shadow transition-all transform hover:-translate-y-0.5 font-medium"
                  >
                    Consultar Disponibilidad
                  </a>
                </div>
              </div>
            )}
            
            {/* Modalidad 2 */}
            {activeTab === 'modalidad-2' && (
              <div className="flex flex-col lg:flex-row gap-8">
                <div className="lg:w-1/2">
                  <div className="rounded-lg overflow-hidden shadow-lg h-80">
                    <img 
                      src="https://images.unsplash.com/photo-1519643381401-22c77e60520e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
                      alt="Amplia sala de estar para grupos grandes" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div className="rounded-lg overflow-hidden shadow-lg h-48">
                      <img 
                        src="https://images.unsplash.com/photo-1540518614846-7eded433c457?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                        alt="Tercer dormitorio para huéspedes adicionales" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    <div className="rounded-lg overflow-hidden shadow-lg h-48">
                      <img 
                        src="https://pixabay.com/get/ga8c026c215d9d1fb86f96c417dc99c79011629e6ce1d35b1c577493017ef3f505be417d8faa63f296d5e35f9705f6e7c50aab8db01d7e4f6b0e457fb3c2a602f_1280.jpg" 
                        alt="Espacioso comedor para grupos grandes" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="lg:w-1/2">
                  <h4 className="text-xl font-serif font-bold text-woodBrown-dark mb-4">Opción Grupo Extendido: Hasta 9-10 Huéspedes</h4>
                  
                  <p className="mb-6 text-stoneGray-dark">
                    Ideal para grupos más grandes, esta configuración aprovecha al máximo los espacios, ofreciendo 3 dormitorios y todas las comodidades para hasta 10 personas, sin sacrificar el confort y la exclusividad.
                  </p>
                  
                  <div className="mb-6">
                    <div className="flex items-center mb-2">
                      <span className="material-icons text-forestGreen mr-2">people</span>
                      <span className="font-medium">Capacidad máxima: 9-10 personas</span>
                    </div>
                    
                    <div className="flex items-center mb-2">
                      <span className="material-icons text-forestGreen mr-2">king_bed</span>
                      <span className="font-medium">3 Dormitorios</span>
                    </div>
                    
                    <div className="flex items-center">
                      <span className="material-icons text-forestGreen mr-2">payments</span>
                      <span className="font-medium">Precio: $120.000 CLP / Noche</span>
                    </div>
                  </div>
                  
                  <a 
                    href="#reservar"
                    onClick={(e) => {
                      e.preventDefault();
                      document.getElementById('reservar')?.scrollIntoView({ behavior: 'smooth' });
                    }}
                    className="inline-block px-6 py-3 bg-woodBrown hover:bg-woodBrown-dark text-white rounded-sm shadow transition-all transform hover:-translate-y-0.5 font-medium"
                  >
                    Consultar Disponibilidad
                  </a>
                </div>
              </div>
            )}
          </div>
        </motion.div>
        
        {/* Equipamiento */}
        <motion.div 
          className="bg-white rounded-lg shadow-xl p-8"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.2 }}
          transition={{ duration: 0.6 }}
        >
          <div className="text-center mb-10">
            <h3 className="text-2xl md:text-3xl font-serif font-bold text-woodBrown-dark mb-4">Equipamiento General</h3>
            <p className="text-stoneGray-dark max-w-3xl mx-auto">
              Todo el equipamiento está disponible de forma EXCLUSIVA para los huéspedes, independientemente de la configuración elegida.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {equipmentItems.map((item, index) => (
              <motion.div 
                key={index}
                className="flex items-start p-6 bg-linen bg-opacity-50 rounded-lg hover:shadow-lg transition-shadow"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <span className="material-icons text-amber-600 text-3xl mr-4">{item.icon}</span>
                <div>
                  <h4 className="font-medium text-woodBrown-dark mb-2">{item.title}</h4>
                  <p className="text-sm text-stoneGray-dark">{item.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
          
          <div className="mt-10 p-6 bg-amber-500 bg-opacity-10 border-l-4 border-amber-600 rounded-r-lg">
            <h4 className="font-serif font-bold text-woodBrown-dark mb-2">IMPORTANTE:</h4>
            <p className="text-stoneGray-dark">
              Al arrendar nuestra cabaña, independientemente de la configuración de dormitorios que elija, usted y su grupo tendrán <strong>ACCESO EXCLUSIVO Y PRIVADO</strong> a TODAS las instalaciones mencionadas (quincho, piscinas, estacionamiento, áreas de juego, etc.). La cabaña completa y sus terrenos son solo para ustedes durante su estadía, garantizando total privacidad.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CabanaSection;

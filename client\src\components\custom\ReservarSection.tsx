import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { apiRequest } from '@/lib/queryClient';

interface ReservarSectionProps {
  onFormSuccess: () => void;
}

type ConfigurationType = 'config-2' | 'config-3';

interface FormData {
  checkIn: string;
  checkOut: string;
  configurationType: ConfigurationType;
  adults: number;
  children: number;
  fullName: string;
  email: string;
  phone: string;
  specialRequests: string;
  policiesAccepted: boolean;
}

const ReservarSection: React.FC<ReservarSectionProps> = ({ onFormSuccess }) => {
  // Today and tomorrow dates for min attributes
  const today = new Date().toISOString().split('T')[0];
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const tomorrowStr = tomorrow.toISOString().split('T')[0];

  const [formData, setFormData] = useState<FormData>({
    checkIn: '',
    checkOut: '',
    configurationType: 'config-2',
    adults: 2,
    children: 0,
    fullName: '',
    email: '',
    phone: '',
    specialRequests: '',
    policiesAccepted: false
  });
  
  const [formErrors, setFormErrors] = useState<Partial<Record<keyof FormData, string>>>({});
  const [loading, setLoading] = useState(false);
  
  // Booking summary calculation
  const [summary, setSummary] = useState({
    configName: '2 Dormitorios',
    pricePerNight: 90000,
    nightsCount: 0,
    totalPrice: 0
  });

  // Update booking summary when relevant form data changes
  useEffect(() => {
    // Configuration details
    const configName = formData.configurationType === 'config-2' ? '2 Dormitorios' : '3 Dormitorios';
    const pricePerNight = formData.configurationType === 'config-2' ? 90000 : 120000;
    
    // Nights calculation
    let nightsCount = 0;
    let totalPrice = 0;
    
    if (formData.checkIn && formData.checkOut) {
      const checkInDate = new Date(formData.checkIn);
      const checkOutDate = new Date(formData.checkOut);
      
      // Calculate difference in days
      const timeDiff = checkOutDate.getTime() - checkInDate.getTime();
      nightsCount = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      
      // Calculate total price
      if (nightsCount > 0) {
        totalPrice = nightsCount * pricePerNight;
      }
    }
    
    setSummary({ configName, pricePerNight, nightsCount, totalPrice });
  }, [formData.checkIn, formData.checkOut, formData.configurationType]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    // Handle checkbox input
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    
    // Clear error for this field when user types
    if (formErrors[name as keyof FormData]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }));
    }
    
    // Special handling for checkIn date to update checkOut min date
    if (name === 'checkIn' && value) {
      const checkInDate = new Date(value);
      const nextDay = new Date(checkInDate);
      nextDay.setDate(nextDay.getDate() + 1);
      
      // If current checkOut date is before new checkIn date, update it
      if (formData.checkOut && new Date(formData.checkOut) <= checkInDate) {
        const nextDayStr = nextDay.toISOString().split('T')[0];
        setFormData(prev => ({ ...prev, checkOut: nextDayStr }));
      }
    }
  };

  // Handle configuration option selection
  const handleConfigSelection = (configType: ConfigurationType) => {
    setFormData(prev => ({ ...prev, configurationType: configType }));
  };

  // Form validation
  const validateForm = () => {
    const errors: Partial<Record<keyof FormData, string>> = {};
    
    if (!formData.checkIn) {
      errors.checkIn = 'La fecha de llegada es requerida';
    }
    
    if (!formData.checkOut) {
      errors.checkOut = 'La fecha de salida es requerida';
    }
    
    if (!formData.fullName.trim()) {
      errors.fullName = 'El nombre es requerido';
    }
    
    if (!formData.email.trim()) {
      errors.email = 'El correo electrónico es requerido';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Ingrese un correo electrónico válido';
    }
    
    if (!formData.phone.trim()) {
      errors.phone = 'El teléfono es requerido para contactarte';
    }
    
    if (!formData.policiesAccepted) {
      errors.policiesAccepted = 'Debes aceptar las políticas de reserva y cancelación';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setLoading(true);
    
    try {
      await apiRequest('POST', '/api/booking', {
        ...formData,
        // Add summary information
        pricePerNight: summary.pricePerNight,
        nightsCount: summary.nightsCount,
        totalPrice: summary.totalPrice
      });
      
      // Reset form
      setFormData({
        checkIn: '',
        checkOut: '',
        configurationType: 'config-2',
        adults: 2,
        children: 0,
        fullName: '',
        email: '',
        phone: '',
        specialRequests: '',
        policiesAccepted: false
      });
      
      // Call success callback
      onFormSuccess();
    } catch (error) {
      console.error('Error submitting booking:', error);
      // Handle error (could show toast message here)
    } finally {
      setLoading(false);
    }
  };

  // Format price to CLP
  const formatPrice = (price: number) => {
    return `$${price.toLocaleString('es-CL')} CLP`;
  };

  return (
    <section id="reservar" className="py-20 bg-linen relative">
      {/* Background texture */}
      <div className="absolute inset-0 opacity-10 pointer-events-none bg-grain"></div>

      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <motion.h2 
            className="text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-woodBrown-dark mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5 }}
          >
            Reserva Tu Estadía
          </motion.h2>
          <motion.p 
            className="text-lg text-stoneGray-dark max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Asegura tu lugar en nuestra exclusiva cabaña y vive una experiencia inolvidable en Pica.
          </motion.p>
        </div>
        
        <motion.div 
          className="bg-white rounded-lg shadow-premium p-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.2 }}
          transition={{ duration: 0.6 }}
        >
          <form onSubmit={handleSubmit} className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Dates Selection */}
              <div>
                <h3 className="text-xl font-serif font-bold text-woodBrown-dark mb-6">Fechas y Configuración</h3>
                
                <div className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="checkIn" className="block text-sm font-medium text-stoneGray-dark mb-2">
                        Fecha de Llegada
                      </label>
                      <input 
                        type="date" 
                        id="checkIn" 
                        name="checkIn" 
                        className={`w-full px-4 py-3 border ${formErrors.checkIn ? 'border-red-500' : 'border-stoneGray-light'} rounded-sm focus:outline-none focus:ring-2 focus:ring-amberGold-light focus:border-transparent transition-all`}
                        min={today}
                        value={formData.checkIn}
                        onChange={handleInputChange}
                      />
                      {formErrors.checkIn && (
                        <p className="mt-1 text-sm text-red-500">{formErrors.checkIn}</p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="checkOut" className="block text-sm font-medium text-stoneGray-dark mb-2">
                        Fecha de Salida
                      </label>
                      <input 
                        type="date" 
                        id="checkOut" 
                        name="checkOut" 
                        className={`w-full px-4 py-3 border ${formErrors.checkOut ? 'border-red-500' : 'border-stoneGray-light'} rounded-sm focus:outline-none focus:ring-2 focus:ring-amberGold-light focus:border-transparent transition-all`}
                        min={formData.checkIn ? new Date(new Date(formData.checkIn).getTime() + 86400000).toISOString().split('T')[0] : tomorrowStr}
                        value={formData.checkOut}
                        onChange={handleInputChange}
                      />
                      {formErrors.checkOut && (
                        <p className="mt-1 text-sm text-red-500">{formErrors.checkOut}</p>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-stoneGray-dark mb-2">
                      Tipo de Configuración
                    </label>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div 
                        className={`border ${formData.configurationType === 'config-2' ? 'border-amberGold-dark' : 'border-stoneGray-light'} p-4 rounded-sm hover:border-amberGold transition-colors cursor-pointer`}
                        onClick={() => handleConfigSelection('config-2')}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium text-woodBrown-dark">Configuración 2 Dormitorios</h4>
                          <span className={`inline-block w-5 h-5 rounded-full border-2 ${formData.configurationType === 'config-2' ? 'border-amberGold-dark' : 'border-stoneGray-light'} flex items-center justify-center`}>
                            <span className={`inline-block w-2 h-2 rounded-full ${formData.configurationType === 'config-2' ? 'bg-amberGold-dark' : 'bg-stoneGray-light'}`}></span>
                          </span>
                        </div>
                        
                        <p className="text-sm text-stoneGray-dark mb-2">Capacidad máxima: 6 personas</p>
                        <p className="text-amberGold-dark font-medium">$90.000 CLP / Noche</p>
                      </div>
                      
                      <div 
                        className={`border ${formData.configurationType === 'config-3' ? 'border-amberGold-dark' : 'border-stoneGray-light'} p-4 rounded-sm hover:border-amberGold transition-colors cursor-pointer`}
                        onClick={() => handleConfigSelection('config-3')}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium text-woodBrown-dark">Configuración 3 Dormitorios</h4>
                          <span className={`inline-block w-5 h-5 rounded-full border-2 ${formData.configurationType === 'config-3' ? 'border-amberGold-dark' : 'border-stoneGray-light'} flex items-center justify-center`}>
                            <span className={`inline-block w-2 h-2 rounded-full ${formData.configurationType === 'config-3' ? 'bg-amberGold-dark' : 'bg-stoneGray-light'}`}></span>
                          </span>
                        </div>
                        
                        <p className="text-sm text-stoneGray-dark mb-2">Capacidad máxima: 9-10 personas</p>
                        <p className="text-amberGold-dark font-medium">$120.000 CLP / Noche</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="adults" className="block text-sm font-medium text-stoneGray-dark mb-2">
                        Adultos
                      </label>
                      <select 
                        id="adults" 
                        name="adults" 
                        className="w-full px-4 py-3 border border-stoneGray-light rounded-sm focus:outline-none focus:ring-2 focus:ring-amberGold-light focus:border-transparent transition-all"
                        value={formData.adults}
                        onChange={handleInputChange}
                      >
                        {[1, 2, 3, 4, 5, 6, 7, 8].map(num => (
                          <option key={num} value={num}>
                            {num} {num === 1 ? 'adulto' : 'adultos'}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="children" className="block text-sm font-medium text-stoneGray-dark mb-2">
                        Niños
                      </label>
                      <select 
                        id="children" 
                        name="children" 
                        className="w-full px-4 py-3 border border-stoneGray-light rounded-sm focus:outline-none focus:ring-2 focus:ring-amberGold-light focus:border-transparent transition-all"
                        value={formData.children}
                        onChange={handleInputChange}
                      >
                        {[0, 1, 2, 3, 4].map(num => (
                          <option key={num} value={num}>
                            {num} {num === 1 ? 'niño' : 'niños'}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Personal Information */}
              <div>
                <h3 className="text-xl font-serif font-bold text-woodBrown-dark mb-6">Información Personal</h3>
                
                <div className="space-y-6">
                  <div>
                    <label htmlFor="fullName" className="block text-sm font-medium text-stoneGray-dark mb-2">
                      Nombre Completo
                    </label>
                    <input 
                      type="text" 
                      id="fullName" 
                      name="fullName" 
                      className={`w-full px-4 py-3 border ${formErrors.fullName ? 'border-red-500' : 'border-stoneGray-light'} rounded-sm focus:outline-none focus:ring-2 focus:ring-amberGold-light focus:border-transparent transition-all`}
                      placeholder="Tu nombre completo"
                      value={formData.fullName}
                      onChange={handleInputChange}
                    />
                    {formErrors.fullName && (
                      <p className="mt-1 text-sm text-red-500">{formErrors.fullName}</p>
                    )}
                  </div>
                  
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-stoneGray-dark mb-2">
                      Correo Electrónico
                    </label>
                    <input 
                      type="email" 
                      id="email" 
                      name="email" 
                      className={`w-full px-4 py-3 border ${formErrors.email ? 'border-red-500' : 'border-stoneGray-light'} rounded-sm focus:outline-none focus:ring-2 focus:ring-amberGold-light focus:border-transparent transition-all`}
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={handleInputChange}
                    />
                    {formErrors.email && (
                      <p className="mt-1 text-sm text-red-500">{formErrors.email}</p>
                    )}
                  </div>
                  
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-stoneGray-dark mb-2">
                      Teléfono
                    </label>
                    <input 
                      type="tel" 
                      id="phone" 
                      name="phone" 
                      className={`w-full px-4 py-3 border ${formErrors.phone ? 'border-red-500' : 'border-stoneGray-light'} rounded-sm focus:outline-none focus:ring-2 focus:ring-amberGold-light focus:border-transparent transition-all`}
                      placeholder="+56 9 1234 5678"
                      value={formData.phone}
                      onChange={handleInputChange}
                    />
                    {formErrors.phone && (
                      <p className="mt-1 text-sm text-red-500">{formErrors.phone}</p>
                    )}
                  </div>
                  
                  <div>
                    <label htmlFor="specialRequests" className="block text-sm font-medium text-stoneGray-dark mb-2">
                      Peticiones Especiales (Opcional)
                    </label>
                    <textarea 
                      id="specialRequests" 
                      name="specialRequests" 
                      rows={3} 
                      className="w-full px-4 py-3 border border-stoneGray-light rounded-sm focus:outline-none focus:ring-2 focus:ring-amberGold-light focus:border-transparent transition-all resize-none"
                      placeholder="¿Tienes alguna petición especial?"
                      value={formData.specialRequests}
                      onChange={handleInputChange}
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Pricing Summary */}
            <div className="bg-linen bg-opacity-50 p-6 rounded-lg">
              <h3 className="text-xl font-serif font-bold text-woodBrown-dark mb-4">Resumen de Precios</h3>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Configuración seleccionada:</span>
                  <span id="config-summary">{summary.configName}</span>
                </div>
                
                <div className="flex justify-between">
                  <span>Precio por noche:</span>
                  <span id="price-night">{formatPrice(summary.pricePerNight)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span>Noches:</span>
                  <span id="nights-count">{summary.nightsCount}</span>
                </div>
                
                <div className="border-t border-stoneGray-light pt-2 mt-2">
                  <div className="flex justify-between font-bold">
                    <span>Total:</span>
                    <span id="total-price">{formatPrice(summary.totalPrice)}</span>
                  </div>
                </div>
              </div>
              
              <div className="mt-4 text-sm text-stoneGray-dark">
                <p>* Precios en pesos chilenos, incluyen IVA.</p>
                <p>* Se solicitará un 50% de anticipo para confirmar la reserva.</p>
              </div>
            </div>
            
            {/* Policies */}
            <div className="border-t border-stoneGray-light pt-6">
              <div className="mb-6">
                <h4 className="font-medium text-woodBrown-dark mb-2">Políticas de Reserva y Cancelación:</h4>
                <ul className="text-sm text-stoneGray-dark list-disc list-inside space-y-1">
                  <li>Hora de check-in: 15:00 hrs. / Hora de check-out: 12:00 hrs.</li>
                  <li>Cancelación gratuita hasta 7 días antes de la llegada.</li>
                  <li>Entre 7 y 3 días: 50% del anticipo.</li>
                  <li>Menos de 3 días o no presentación: 100% del anticipo.</li>
                </ul>
              </div>
              
              <div className="flex items-start mb-6">
                <input 
                  type="checkbox" 
                  id="policiesAccepted" 
                  name="policiesAccepted" 
                  className="mt-1"
                  checked={formData.policiesAccepted}
                  onChange={handleInputChange}
                />
                <label htmlFor="policiesAccepted" className="ml-2 text-sm text-stoneGray-dark">
                  He leído y acepto las políticas de reserva y cancelación.
                </label>
              </div>
              {formErrors.policiesAccepted && (
                <p className="mb-4 text-sm text-red-500">{formErrors.policiesAccepted}</p>
              )}
              
              <button 
                type="submit" 
                className="w-full px-6 py-4 bg-amberGold hover:bg-amberGold-light text-woodBrown-dark rounded-sm shadow-lg transition-all transform hover:-translate-y-0.5 font-bold text-lg flex items-center justify-center"
                disabled={loading}
              >
                {loading ? (
                  <span className="inline-block animate-spin mr-2 material-icons">
                    autorenew
                  </span>
                ) : (
                  'Solicitar Reserva'
                )}
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </section>
  );
};

export default ReservarSection;

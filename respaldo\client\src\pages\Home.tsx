import React, { useState } from 'react';
import Header from '@/components/custom/Header';
import HeroSection from '@/components/custom/HeroSection';
import CabanaSection from '@/components/custom/CabanaSection';
import UbicacionSection from '@/components/custom/UbicacionSection';
import GaleriaSection from '@/components/custom/GaleriaSection';
import ContactSection from '@/components/custom/ContactSection';
import ReservarSection from '@/components/custom/ReservarSection';
import Footer from '@/components/custom/Footer';
import SuccessModal from '@/components/custom/SuccessModal';
import { motion } from 'framer-motion';

const Home: React.FC = () => {
  const [successModalOpen, setSuccessModalOpen] = useState(false);
  const [modalMessage, setModalMessage] = useState({
    title: '',
    message: ''
  });

  // Function to be passed to contact/booking forms
  const handleFormSuccess = (formType: 'contact' | 'booking') => {
    if (formType === 'contact') {
      setModalMessage({
        title: '¡Mensaje Enviado!',
        message: 'Tu mensaje ha sido enviado correctamente. Nos pondremos en contacto contigo a la brevedad posible.'
      });
    } else {
      setModalMessage({
        title: '¡Solicitud Recibida!',
        message: 'Tu solicitud de reserva ha sido enviada con éxito. Nos pondremos en contacto contigo a la brevedad para confirmar disponibilidad y coordinar el pago del anticipo.'
      });
    }
    setSuccessModalOpen(true);
  };

  // Back to top button
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <div className="relative">
      <Header />
      
      <main>
        <HeroSection />
        <CabanaSection />
        <UbicacionSection />
        <GaleriaSection />
        <ContactSection onFormSuccess={() => handleFormSuccess('contact')} />
        <ReservarSection onFormSuccess={() => handleFormSuccess('booking')} />
      </main>
      
      <Footer />
      
      {/* Back to Top Button */}
      <motion.button 
        id="back-to-top" 
        className="fixed bottom-8 right-8 z-10 p-2 rounded-full bg-woodBrown text-white shadow-lg"
        onClick={scrollToTop}
        initial={{ opacity: 0, y: 20 }}
        animate={{ 
          opacity: window.scrollY > 300 ? 1 : 0,
          y: window.scrollY > 300 ? 0 : 20
        }}
        transition={{ duration: 0.3 }}
      >
        <span className="material-icons">arrow_upward</span>
      </motion.button>
      
      {/* Success Modal */}
      <SuccessModal
        isOpen={successModalOpen}
        onClose={() => setSuccessModalOpen(false)}
        title={modalMessage.title}
        message={modalMessage.message}
      />
    </div>
  );
};

export default Home;

import React, { useEffect, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ProgressCircle } from '@/components/ui/progress-circle';
import AnimatedText from '@/components/common/AnimatedText';

interface PreloaderProps {
  onLoadComplete: () => void;
}

const Preloader: React.FC<PreloaderProps> = ({ onLoadComplete }) => {
  const [loading, setLoading] = useState(true);
  const [progress, setProgress] = useState(0);
  const [showLogo, setShowLogo] = useState(false);
  const starsRef = useRef<HTMLDivElement>(null);

  // Create animated stars
  useEffect(() => {
    if (starsRef.current) {
      const starsContainer = starsRef.current;
      
      // Clear any existing stars
      starsContainer.innerHTML = '';
      
      // Create stars
      for (let i = 0; i < 100; i++) {
        const star = document.createElement('div');
        const size = Math.random() * 3 + 1;
        
        // Random position
        star.style.width = `${size}px`;
        star.style.height = `${size}px`;
        star.style.left = `${Math.random() * 100}%`;
        star.style.top = `${Math.random() * 100}%`;
        
        // Style
        star.style.position = 'absolute';
        star.style.backgroundColor = 'white';
        star.style.borderRadius = '50%';
        star.style.opacity = `${Math.random() * 0.7 + 0.3}`;
        
        // Animation
        const duration = 2 + Math.random() * 3;
        star.style.animation = `twinkle ${duration}s infinite alternate ease-in-out`;
        star.style.animationDelay = `${Math.random() * 5}s`;
        
        starsContainer.appendChild(star);
      }
    }
  }, []);

  // Simulate loading progress
  useEffect(() => {
    const timeout = setTimeout(() => {
      setShowLogo(true);
    }, 500);

    let interval: NodeJS.Timeout;
    
    if (loading) {
      let value = 0;
      interval = setInterval(() => {
        value += Math.floor(Math.random() * 3) + 1;
        
        if (value >= 100) {
          value = 100;
          setProgress(value);
          clearInterval(interval);
          
          // Finish loading after a slight delay
          setTimeout(() => {
            setLoading(false);
            
            // Notify parent when animation is complete
            setTimeout(onLoadComplete, 500);
          }, 1000);
        } else {
          setProgress(value);
        }
      }, 100);
    }
    
    return () => {
      clearTimeout(timeout);
      clearInterval(interval);
    };
  }, [loading, onLoadComplete]);

  return (
    <AnimatePresence>
      {loading && (
        <motion.div
          className="fixed inset-0 bg-black z-50 grid place-items-center overflow-hidden"
          exit={{ opacity: 0 }}
          transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
        >
          {/* Background image */}
          <div className="absolute inset-0 bg-cover bg-center" 
               style={{
                 backgroundImage: "url('https://images.unsplash.com/photo-1542273917363-3b1817f69a2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80')",
                 filter: "brightness(0.6)"
               }}>
            <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70"></div>
          </div>
          
          {/* Animated stars */}
          <div ref={starsRef} className="absolute inset-0"></div>
          
          {/* Campfire animation - hidden for cleaner centered layout */}
          <div className="hidden">
            <div className="relative w-20 h-20">
              <motion.div 
                className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-12 bg-amber-500 rounded-full blur-md"
                animate={{ 
                  height: ["12px", "16px", "12px"],
                  opacity: [0.7, 0.9, 0.7]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              />
              <motion.div 
                className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-16 bg-amber-400 rounded-full blur-lg opacity-70"
                animate={{ 
                  height: ["16px", "20px", "16px"],
                  width: ["12px", "15px", "12px"],
                  opacity: [0.5, 0.7, 0.5]
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              />
            </div>
          </div>
          
          {/* Logo and loader - centered container */}
          <div className="z-10 flex flex-col items-center justify-center max-w-lg px-6 relative">
            {showLogo && (
              <>
                <motion.div 
                  className="relative text-center w-full mb-8"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.7 }}
                >
                  <div className="text-effect">
                    <AnimatedText 
                      text="LA POSADA"
                      className="text-4xl md:text-6xl font-rustic font-bold text-white uppercase tracking-wider"
                      type="characters"
                      tag="h1"
                    />
                  </div>
                  <div className="flex items-center justify-center my-2">
                    <div className="h-1 bg-amber-500 w-16 mx-3 rounded-full shadow-glow"></div>
                    <motion.div 
                      initial={{ scale: 0 }}
                      animate={{ scale: 1, rotate: [0, 10, 0] }}
                      transition={{ delay: 1.5, duration: 0.8, type: "spring" }}
                      className="h-8 w-8 bg-amber-500 rounded-full flex items-center justify-center shadow-glow"
                    >
                      <span className="material-icons text-woodBrown-dark text-sm">pets</span>
                    </motion.div>
                    <div className="h-1 bg-amber-500 w-16 mx-3 rounded-full shadow-glow"></div>
                  </div>
                  <div className="text-effect gold">
                    <AnimatedText 
                      text="DEL OSO"
                      className="text-4xl md:text-6xl font-rustic font-bold text-amber-400 uppercase tracking-wider"
                      type="characters"
                      tag="h1"
                      delay={800}
                    />
                  </div>
                  
                  <motion.div 
                    className="absolute -z-10 inset-0 bg-woodBrown-dark bg-opacity-30 blur-xl rounded-full"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 0.6, scale: 1.1 }}
                    transition={{ 
                      delay: 1.2, 
                      duration: 2, 
                      repeat: Infinity, 
                      repeatType: "reverse" 
                    }}
                  />
                </motion.div>
                
                <motion.div 
                  className="w-full max-w-xs text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.2, duration: 0.8 }}
                >
                  <ProgressCircle value={progress} className="w-20 h-20 mx-auto mb-4 shadow-glow">
                    <div className="text-white font-bold text-sm">{progress}%</div>
                  </ProgressCircle>
                  
                  <motion.p 
                    className="text-amber-50 text-center tracking-widest text-sm uppercase font-rustic"
                    animate={{ opacity: [0.7, 1, 0.7] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    Cargando experiencia...
                  </motion.p>
                </motion.div>
              </>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Preloader;

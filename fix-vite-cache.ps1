# Script para Solucionar Problemas de Caché de Vite
# Autor: Augment Agent
# Descripción: Limpia el caché de Vite y npm para resolver errores de permisos

Write-Host "🔧 Iniciando limpieza de caché de Vite y npm..." -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

# Detener procesos de Node.js que puedan estar bloqueando archivos
Write-Host "🛑 Deteniendo procesos de Node.js..." -ForegroundColor Yellow
try {
    Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Procesos de Node.js detenidos." -ForegroundColor Green
} catch {
    Write-Host "ℹ️ No se encontraron procesos de Node.js ejecutándose." -ForegroundColor Blue
}

# Esperar un momento para que los procesos se liberen completamente
Start-Sleep -Seconds 2

# Limpiar caché de Vite
Write-Host "🗑️ Limpiando caché de Vite..." -ForegroundColor Yellow
$viteCachePath = "node_modules\.vite"
if (Test-Path $viteCachePath) {
    try {
        Remove-Item -Path $viteCachePath -Recurse -Force -ErrorAction Stop
        Write-Host "✅ Caché de Vite eliminado correctamente." -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Error al eliminar caché de Vite: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "💡 Intentando con método alternativo..." -ForegroundColor Yellow
        
        # Método alternativo usando cmd
        try {
            cmd /c "rmdir /s /q `"$viteCachePath`""
            Write-Host "✅ Caché de Vite eliminado con método alternativo." -ForegroundColor Green
        } catch {
            Write-Host "❌ No se pudo eliminar el caché de Vite. Intenta manualmente." -ForegroundColor Red
        }
    }
} else {
    Write-Host "ℹ️ No se encontró caché de Vite para eliminar." -ForegroundColor Blue
}

# Limpiar caché de npm
Write-Host "🗑️ Limpiando caché de npm..." -ForegroundColor Yellow
try {
    npm cache clean --force
    Write-Host "✅ Caché de npm limpiado correctamente." -ForegroundColor Green
} catch {
    Write-Host "⚠️ Error al limpiar caché de npm: $($_.Exception.Message)" -ForegroundColor Red
}

# Limpiar archivos temporales adicionales
Write-Host "🗑️ Limpiando archivos temporales adicionales..." -ForegroundColor Yellow
$tempPaths = @(
    "dist",
    ".vite",
    "node_modules\.cache"
)

foreach ($tempPath in $tempPaths) {
    if (Test-Path $tempPath) {
        try {
            Remove-Item -Path $tempPath -Recurse -Force -ErrorAction Stop
            Write-Host "✅ Eliminado: $tempPath" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ No se pudo eliminar: $tempPath" -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "🎉 Limpieza completada!" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "💡 Sugerencias:" -ForegroundColor Cyan
Write-Host "   1. Ejecuta 'npm run dev' para reiniciar el servidor" -ForegroundColor White
Write-Host "   2. Si el problema persiste, reinicia tu terminal" -ForegroundColor White
Write-Host "   3. Como último recurso, reinicia tu computadora" -ForegroundColor White
Write-Host ""

# Preguntar si desea reiniciar el servidor automáticamente
$restart = Read-Host "¿Deseas reiniciar el servidor de desarrollo ahora? (s/n)"
if ($restart -eq "s" -or $restart -eq "S" -or $restart -eq "si" -or $restart -eq "Si") {
    Write-Host "🚀 Iniciando servidor de desarrollo..." -ForegroundColor Green
    npm run dev
} else {
    Write-Host "ℹ️ Puedes reiniciar el servidor manualmente con 'npm run dev'" -ForegroundColor Blue
}

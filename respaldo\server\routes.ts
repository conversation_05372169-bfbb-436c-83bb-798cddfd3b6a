import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import path from "path";

export async function registerRoutes(app: Express): Promise<Server> {
  // API prefix for all routes
  const apiPrefix = '/api';

  // Ruta para instrucciones de certificado SSL
  app.get('/cert-help', (req, res) => {
    res.sendFile(path.resolve(process.cwd(), 'client/public/cert-instructions.html'));
  });

  // Contact form submission
  app.post(`${apiPrefix}/contact`, async (req, res) => {
    try {
      const { name, email, phone, subject, message } = req.body;

      // Validate required fields
      if (!name || !email || !subject || !message) {
        return res.status(400).json({
          message: 'Por favor complete todos los campos requeridos'
        });
      }

      // Store the contact request
      const contactData = {
        name,
        email,
        phone: phone || '',
        subject,
        message,
        createdAt: new Date()
      };

      const savedContact = await storage.createContact(contactData);

      // In a real application, here you might:
      // 1. Send an email notification
      // 2. Add to a CRM system
      // 3. Create a task in a project management tool

      return res.status(200).json({
        message: 'Mensaje enviado correctamente',
        id: savedContact.id
      });
    } catch (error) {
      console.error('Error processing contact form:', error);
      return res.status(500).json({
        message: 'Error al procesar la solicitud. Por favor intente nuevamente.'
      });
    }
  });

  // Booking request submission
  app.post(`${apiPrefix}/booking`, async (req, res) => {
    try {
      const {
        checkIn,
        checkOut,
        configurationType,
        adults,
        children,
        fullName,
        email,
        phone,
        specialRequests,
        pricePerNight,
        nightsCount,
        totalPrice
      } = req.body;

      // Validate required fields
      if (!checkIn || !checkOut || !configurationType || !fullName || !email || !phone) {
        return res.status(400).json({
          message: 'Por favor complete todos los campos requeridos'
        });
      }

      // Validate date format and ensure checkOut is after checkIn
      const checkInDate = new Date(checkIn);
      const checkOutDate = new Date(checkOut);

      if (isNaN(checkInDate.getTime()) || isNaN(checkOutDate.getTime())) {
        return res.status(400).json({ message: 'Formato de fecha inválido' });
      }

      if (checkOutDate <= checkInDate) {
        return res.status(400).json({
          message: 'La fecha de salida debe ser posterior a la fecha de llegada'
        });
      }

      // Store the booking
      const bookingData = {
        checkIn: checkInDate,
        checkOut: checkOutDate,
        configurationType,
        guests: {
          adults: Number(adults),
          children: Number(children),
        },
        contactInfo: {
          fullName,
          email,
          phone,
        },
        specialRequests: specialRequests || '',
        pricing: {
          pricePerNight: Number(pricePerNight),
          nightsCount: Number(nightsCount),
          totalPrice: Number(totalPrice),
        },
        status: 'pending',
        createdAt: new Date()
      };

      const savedBooking = await storage.createBooking(bookingData);

      // In a real application, here you might:
      // 1. Check actual availability
      // 2. Send confirmation emails
      // 3. Process payment or create payment link

      return res.status(200).json({
        message: 'Solicitud de reserva recibida correctamente',
        id: savedBooking.id,
        status: savedBooking.status
      });
    } catch (error) {
      console.error('Error processing booking request:', error);
      return res.status(500).json({
        message: 'Error al procesar la reserva. Por favor intente nuevamente.'
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}

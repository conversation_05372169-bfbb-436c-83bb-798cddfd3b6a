import React, { ReactNode, useRef, useEffect, useState } from 'react';

interface ParallaxSectionProps {
  children: ReactNode;
  backgroundImage: string;
  overlayColor?: string;
  speed?: number;
  className?: string;
}

const ParallaxSection: React.FC<ParallaxSectionProps> = ({
  children,
  backgroundImage,
  overlayColor = 'rgba(0, 0, 0, 0.4)',
  speed = 0.5,
  className = '',
}) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const [offsetY, setOffsetY] = useState(0);
  const [visible, setVisible] = useState(false);

  // Track scroll position
  useEffect(() => {
    const handleScroll = () => {
      if (sectionRef.current) {
        const { top, height } = sectionRef.current.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        
        // Only update parallax when element is in view
        if (top < windowHeight && top + height > 0) {
          setVisible(true);
          
          // Calculate how far the element is from the center of viewport
          const centerDistance = top - windowHeight / 2 + height / 2;
          setOffsetY(centerDistance * speed);
        } else {
          setVisible(false);
        }
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Initial position
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed]);

  const backgroundStyle = {
    backgroundImage: `url(${backgroundImage})`,
    transform: visible ? `translateY(${offsetY}px)` : 'translateY(0)',
    transition: 'transform 0.1s ease-out',
  };

  const overlayStyle = {
    backgroundColor: overlayColor,
  };

  return (
    <div ref={sectionRef} className={`relative overflow-hidden ${className}`}>
      <div
        className="absolute inset-0 bg-cover bg-center -z-10"
        style={backgroundStyle}
      />
      <div className="absolute inset-0 -z-10" style={overlayStyle} />
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export default ParallaxSection;

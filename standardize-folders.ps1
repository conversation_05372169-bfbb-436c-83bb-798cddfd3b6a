# Definir la ruta base
$basePath = "client\src\assets\atracciones"

# Definir las carpetas estandarizadas
$standardFolders = @(
    "cocha-resbaladero",
    "salar-del-huasco",
    "pueblo-de-pica",
    "parque-dinosaurios",
    "reserva-pampa-del-tamarugal",
    "iglesia-san-andres"
)

# Verificar si existen las carpetas y crear las que faltan
foreach ($folder in $standardFolders) {
    $folderPath = Join-Path -Path $basePath -ChildPath $folder
    
    if (Test-Path $folderPath) {
        Write-Host "La carpeta '$folder' ya existe." -ForegroundColor Green
    } else {
        Write-Host "Creando carpeta '$folder'..." -ForegroundColor Yellow
        New-Item -Path $folderPath -ItemType Directory | Out-Null
        Write-Host "Carpeta '$folder' creada exitosamente." -ForegroundColor Green
    }
}

# Mapeo de carpetas antiguas a nuevas
$folderMapping = @{
    "Salar-del-Huasco" = "salar-del-huasco"
    "Pueblo-de-pica" = "pueblo-de-pica"
    "Reserva-Pampa-del-Tamarugal" = "reserva-pampa-del-tamarugal"
}

# Mover archivos de carpetas con nombres no estandarizados a las nuevas carpetas
foreach ($oldFolder in $folderMapping.Keys) {
    $oldFolderPath = Join-Path -Path $basePath -ChildPath $oldFolder
    $newFolderPath = Join-Path -Path $basePath -ChildPath $folderMapping[$oldFolder]
    
    if (Test-Path $oldFolderPath) {
        Write-Host "Moviendo archivos de '$oldFolder' a '$($folderMapping[$oldFolder])'..." -ForegroundColor Yellow
        
        # Obtener todos los archivos en la carpeta antigua
        $files = Get-ChildItem -Path $oldFolderPath -File
        
        foreach ($file in $files) {
            $destinationPath = Join-Path -Path $newFolderPath -ChildPath $file.Name
            
            # Verificar si el archivo ya existe en la carpeta de destino
            if (Test-Path $destinationPath) {
                Write-Host "El archivo '$($file.Name)' ya existe en la carpeta de destino. Omitiendo..." -ForegroundColor Yellow
            } else {
                # Mover el archivo
                Move-Item -Path $file.FullName -Destination $destinationPath
                Write-Host "Archivo '$($file.Name)' movido exitosamente." -ForegroundColor Green
            }
        }
        
        # Eliminar la carpeta antigua si está vacía
        if ((Get-ChildItem -Path $oldFolderPath).Count -eq 0) {
            Remove-Item -Path $oldFolderPath -Force
            Write-Host "Carpeta antigua '$oldFolder' eliminada." -ForegroundColor Green
        } else {
            Write-Host "La carpeta antigua '$oldFolder' no está vacía. No se eliminará." -ForegroundColor Yellow
        }
    } else {
        Write-Host "La carpeta antigua '$oldFolder' no existe. Omitiendo..." -ForegroundColor Yellow
    }
}

Write-Host "Proceso completado." -ForegroundColor Cyan

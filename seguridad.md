# Documentación de Seguridad - Proyecto Posada 2.0

Este documento detalla todas las medidas de seguridad implementadas en el proyecto Posada 2.0 hasta la fecha.

## Implementación de HTTPS con Certificados SSL

### Configuración de Certificados SSL
- **Ubicación de certificados**: Los certificados se almacenan en el directorio `certs/`.
- **Tipo de certificados**: Para desarrollo se utilizan certificados autofirmados.
- **Archivos de certificados**:
  - `certs/key.pem`: Clave privada
  - `certs/cert.pem`: Certificado público

### Configuración del Servidor HTTPS
- **Implementación**: Se utiliza el módulo `https` de Node.js para crear un servidor HTTPS.
- **Puerto**: El servidor HTTPS se ejecuta en el puerto 5443.
- **Opciones de configuración**:
  ```javascript
  const httpsOptions = {
    key: fs.readFileSync(path.join(process.cwd(), 'certs/key.pem')),
    cert: fs.readFileSync(path.join(process.cwd(), 'certs/cert.pem')),
    rejectUnauthorized: app.get('env') !== 'development'
  };
  ```
- **Manejo de certificados autofirmados**: En entorno de desarrollo, se configura `rejectUnauthorized: false` para permitir conexiones con certificados autofirmados.

### Redirección de HTTP a HTTPS
- **Servidor HTTP**: Se mantiene un servidor HTTP en el puerto 5080 que redirecciona automáticamente a HTTPS.
- **Middleware de redirección**: Se implementó un middleware que detecta conexiones no seguras y las redirecciona a HTTPS.
  ```javascript
  app.use((req, res, next) => {
    if (app.get('env') === 'production' && !req.secure) {
      const isSecure = req.secure || req.headers['x-forwarded-proto'] === 'https';
      
      if (!isSecure && req.method === 'GET') {
        const host = req.headers.host?.split(':')[0] || 'localhost';
        return res.redirect(301, `https://${host}:5443${req.url}`);
      }
    }
    next();
  });
  ```
- **Redirección inteligente**: En desarrollo, los usuarios son redirigidos a una página de ayuda para certificados (`/cert-help`).

### Página de Ayuda para Certificados
- **Ruta**: `/cert-help`
- **Propósito**: Proporciona instrucciones para aceptar certificados autofirmados en diferentes navegadores.
- **Implementación**: Archivo HTML estático servido por Express.

## Cabeceras de Seguridad

### Strict-Transport-Security (HSTS)
- **Propósito**: Forzar HTTPS en todos los navegadores compatibles.
- **Implementación**:
  ```javascript
  res.setHeader('Strict-Transport-Security', 'max-age=63072000; includeSubDomains; preload');
  ```
- **Configuración**:
  - `max-age=63072000`: El navegador recordará que el sitio solo debe accederse usando HTTPS durante 2 años.
  - `includeSubDomains`: La política se aplica a todos los subdominios.
  - `preload`: Indica que el sitio puede ser incluido en la lista de precarga HSTS de los navegadores.

### X-Content-Type-Options
- **Propósito**: Evitar que el navegador intente MIME-sniffing.
- **Implementación**:
  ```javascript
  res.setHeader('X-Content-Type-Options', 'nosniff');
  ```

### X-Frame-Options
- **Propósito**: Evitar clickjacking (ataques de UI redressing).
- **Implementación**:
  ```javascript
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  ```
- **Configuración**: `SAMEORIGIN` permite que la página sea mostrada en un frame solo si el sitio que lo muestra es el mismo origen.

### X-XSS-Protection
- **Propósito**: Protección XSS en navegadores antiguos.
- **Implementación**:
  ```javascript
  res.setHeader('X-XSS-Protection', '1; mode=block');
  ```
- **Configuración**: `1; mode=block` activa la protección XSS del navegador y bloquea la página si se detecta un ataque.

### Content-Security-Policy (CSP)
- **Propósito**: Restringir fuentes de contenido para prevenir XSS y otros ataques de inyección.
- **Implementación en desarrollo**:
  ```javascript
  res.setHeader(
    'Content-Security-Policy',
    "default-src * 'unsafe-inline' 'unsafe-eval'; script-src * 'unsafe-inline' 'unsafe-eval'; connect-src * 'unsafe-inline'; img-src * data: blob: 'unsafe-inline'; frame-src *; style-src * 'unsafe-inline';"
  );
  ```
- **Implementación en producción**:
  ```javascript
  res.setHeader(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self'"
  );
  ```

### Referrer-Policy
- **Propósito**: Controlar la información del referrer.
- **Implementación**:
  ```javascript
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  ```
- **Configuración**: `strict-origin-when-cross-origin` envía el origen, la ruta y la cadena de consulta cuando se realiza una solicitud del mismo origen, pero solo envía el origen cuando se cruza el origen.

## Otras Medidas de Seguridad

### Validación de Datos
- **Implementación básica**: Se realiza validación de datos en el servidor para las solicitudes de formularios de contacto y reservas.
- **Mejora pendiente**: Implementar validación exhaustiva utilizando Zod u otra biblioteca.

### Manejo de Errores
- **Middleware de errores**: Se implementó un middleware para manejar errores y evitar exponer información sensible.
  ```javascript
  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  ```

## Mejoras de Seguridad Pendientes

1. **Protección CSRF**: Implementar tokens CSRF para proteger formularios.
2. **Almacenamiento seguro de contraseñas**: Utilizar algoritmos de hash seguros como bcrypt.
3. **Limitación de tasa (Rate Limiting)**: Implementar para prevenir ataques de fuerza bruta.
4. **Configuración de CORS**: Definir políticas de CORS adecuadas.
5. **Implementación de autenticación y autorización robustas**.
6. **Migración a una base de datos persistente con consultas parametrizadas**.
7. **Implementación de registro (logging) seguro**.
8. **Escaneo regular de vulnerabilidades**.

---

Este documento será actualizado a medida que se implementen nuevas medidas de seguridad en el proyecto.

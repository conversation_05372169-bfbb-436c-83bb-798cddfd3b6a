import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import "./styles/components.css"; // Importar estilos de componentes

// Importar fuentes locales
import '@fontsource/playfair-display/700.css'; // Fuente decorativa para títulos
import '@fontsource/playfair-display/400.css';
import '@fontsource/cabin/400.css'; // Fuente para texto normal
import '@fontsource/cabin/700.css';

// Add custom theme config for tailwind
window.tailwind = window.tailwind || {};
window.tailwind.config = {
  theme: {
    extend: {
      colors: {
        woodBrown: {
          DEFAULT: 'hsl(var(--wood-brown))',
          light: 'hsl(var(--wood-brown-light))',
          dark: 'hsl(var(--wood-brown-dark))'
        },
        forestGreen: {
          DEFAULT: 'hsl(var(--forest-green))',
          light: 'hsl(var(--forest-green-light))',
          dark: 'hsl(var(--forest-green-dark))'
        },
        amberGold: {
          DEFAULT: 'hsl(var(--amber-gold))',
          light: 'hsl(var(--amber-gold-light))',
          dark: 'hsl(var(--amber-gold-dark))'
        },
        stoneGray: {
          DEFAULT: 'hsl(var(--stone-gray))',
          light: 'hsl(var(--stone-gray-light))',
          dark: 'hsl(var(--stone-gray-dark))'
        },
        linen: {
          DEFAULT: 'hsl(var(--linen))',
          light: 'hsl(var(--linen-light))',
          dark: 'hsl(var(--linen-dark))'
        },
      },
      fontFamily: {
        'serif': ['Playfair Display', 'serif'],
        'sans': ['Cabin', 'Montserrat', 'sans-serif'],
        'rustic': ['Cabin', 'cursive']
      },
      height: {
        '128': '32rem',
        '144': '36rem',
      },
      animation: {
        'fade-in': 'fadeIn 1.2s ease-in-out forwards',
        'fade-in-slow': 'fadeIn 2s ease-in-out forwards',
        'slide-up': 'slideUp 0.9s ease-out forwards',
        'slide-up-delay': 'slideUp 0.9s ease-out 0.3s forwards',
        'scale-in': 'scaleIn 1.2s ease forwards',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        slideUp: {
          '0%': { transform: 'translateY(30px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' }
        },
      },
    }
  }
};

// Add Material Icons
const linkElement = document.createElement('link');
linkElement.rel = 'stylesheet';
linkElement.href = 'https://fonts.googleapis.com/icon?family=Material+Icons';
document.head.appendChild(linkElement);

// Add Google Fonts
const fontLink = document.createElement('link');
fontLink.rel = 'stylesheet';
fontLink.href = 'https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,600;0,700;1,400&family=Montserrat:wght@300;400;500;600;700&display=swap';
document.head.appendChild(fontLink);

// Set title and metadata
document.title = "La Posada del Oso | Cabaña Premium en Pica, Chile | Experiencia de Lujo Rústico";

// Add external Tailwind CDN
const tailwindScript = document.createElement('script');
tailwindScript.src = 'https://cdn.tailwindcss.com';
document.head.appendChild(tailwindScript);

createRoot(document.getElementById("root")!).render(<App />);

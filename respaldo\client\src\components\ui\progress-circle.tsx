import * as React from "react";
import { cn } from "@/lib/utils";

export interface ProgressCircleProps
  extends React.HTMLAttributes<HTMLDivElement> {
  value: number;
  max?: number;
  size?: string;
  strokeWidth?: number;
  indicatorColor?: string;
  trackColor?: string;
}

export const ProgressCircle = React.forwardRef<
  HTMLDivElement,
  ProgressCircleProps
>(
  (
    {
      value,
      max = 100,
      size,
      strokeWidth = 3,
      indicatorColor,
      trackColor = "rgba(255, 255, 255, 0.2)",
      className,
      children,
      ...props
    },
    ref
  ) => {
    const normalizedValue = value / max;
    const percentage = Math.round(normalizedValue * 100);
    const strokeDashoffset = 100 - percentage;

    return (
      <div
        ref={ref}
        className={cn(
          "relative inline-flex items-center justify-center overflow-hidden rounded-full",
          className
        )}
        {...props}
      >
        <svg
          className="w-full h-full transform -rotate-90"
          viewBox="0 0 100 100"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Background track */}
          <circle
            cx="50"
            cy="50"
            r="45"
            fill="none"
            stroke={trackColor}
            strokeWidth={strokeWidth}
          />
          {/* Progress indicator */}
          <circle
            cx="50"
            cy="50"
            r="45"
            fill="none"
            stroke={indicatorColor || "currentColor"}
            strokeWidth={strokeWidth}
            strokeDasharray="283"
            strokeDashoffset={`${strokeDashoffset * 2.83}`}
            strokeLinecap="round"
            style={{ transition: "stroke-dashoffset 0.5s ease-in-out" }}
          />
        </svg>
        <div className="absolute">{children}</div>
      </div>
    );
  }
);

ProgressCircle.displayName = "ProgressCircle";

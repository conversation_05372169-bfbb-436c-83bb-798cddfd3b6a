export const CustomIcons = {
  private: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M18 8H17V6C17 3.24 14.76 1 12 1C9.24 1 7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM12 17C10.9 17 10 16.1 10 15C10 13.9 10.9 13 12 13C13.1 13 14 13.9 14 15C14 16.1 13.1 17 12 17ZM15.1 8H8.9V6C8.9 4.29 10.29 2.9 12 2.9C13.71 2.9 15.1 4.29 15.1 6V8Z" fill="currentColor"/>
  </svg>`,
  
  panorama: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M21 4H3C1.9 4 1 4.9 1 6V18C1 19.1 1.9 20 3 20H21C22.1 20 23 19.1 23 18V6C23 4.9 22.1 4 21 4ZM21 18H3V6H21V18ZM14.5 11L11 15.51L8.5 12.5L5 17H19L14.5 11Z" fill="currentColor"/>
  </svg>`,
  
  quincho: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.5 8.5C12.5 9.33 11.83 10 11 10C10.17 10 9.5 9.33 9.5 8.5C9.5 7.67 10.17 7 11 7C11.83 7 12.5 7.67 12.5 8.5Z" fill="currentColor"/>
    <path d="M12 14.5L14 17H18L15 13.4L12 14.5Z" fill="currentColor"/>
    <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19Z" fill="currentColor"/>
    <path d="M14.04 12H10V11L12 10L14.04 11V12Z" fill="currentColor"/>
    <path d="M6 18H8L10 15V13H6V18Z" fill="currentColor"/>
  </svg>`,
  
  pool: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M2 15C2 13.93 2.87 13.07 3.94 13.01L4 13V7C4 6.45 4.45 6 5 6H13V3.41C12.4 3.14 12 2.61 12 2C12 1.17 12.67 0.5 13.5 0.5C14.33 0.5 15 1.17 15 2C15 2.61 14.6 3.14 14 3.41V6H22C22.55 6 23 6.45 23 7V13H23.06C24.13 13.07 25 13.93 25 15C25 16.07 24.13 16.93 23.06 16.99L23 17V20C23 20.55 22.55 21 22 21H5C4.45 21 4 20.55 4 20V17H3.94C2.87 16.93 2 16.07 2 15ZM21 15C21 14.45 20.55 14 20 14H7C6.45 14 6 14.45 6 15C6 15.55 6.45 16 7 16H20C20.55 16 21 15.55 21 15Z" fill="currentColor"/>
  </svg>`,
  
  woodTexture: `<svg width="300" height="300" viewBox="0 0 300 300" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="300" height="300" fill="#5D4037" opacity="0.05"/>
    <filter id="noise" x="0" y="0" width="300" height="300" filterUnits="userSpaceOnUse">
      <feGaussianBlur stdDeviation="0.5" result="blur"/>
      <feTurbulence type="fractalNoise" baseFrequency="0.1" numOctaves="3" result="noise"/>
      <feComposite in="blur" in2="noise" operator="in" result="noisy-blur"/>
    </filter>
    <rect width="300" height="300" filter="url(#noise)" opacity="0.1"/>
    <path d="M0 0L300 300" stroke="#8D6E63" stroke-width="0.5" stroke-opacity="0.1"/>
    <path d="M20 0L300 280" stroke="#8D6E63" stroke-width="0.5" stroke-opacity="0.1"/>
    <path d="M40 0L300 260" stroke="#8D6E63" stroke-width="0.5" stroke-opacity="0.1"/>
    <path d="M60 0L300 240" stroke="#8D6E63" stroke-width="0.5" stroke-opacity="0.1"/>
    <path d="M80 0L300 220" stroke="#8D6E63" stroke-width="0.5" stroke-opacity="0.1"/>
    <path d="M100 0L300 200" stroke="#8D6E63" stroke-width="0.5" stroke-opacity="0.1"/>
    <path d="M0 20L280 300" stroke="#8D6E63" stroke-width="0.5" stroke-opacity="0.1"/>
    <path d="M0 40L260 300" stroke="#8D6E63" stroke-width="0.5" stroke-opacity="0.1"/>
    <path d="M0 60L240 300" stroke="#8D6E63" stroke-width="0.5" stroke-opacity="0.1"/>
    <path d="M0 80L220 300" stroke="#8D6E63" stroke-width="0.5" stroke-opacity="0.1"/>
    <path d="M0 100L200 300" stroke="#8D6E63" stroke-width="0.5" stroke-opacity="0.1"/>
  </svg>`,
  
  stoneTexture: `<svg width="300" height="300" viewBox="0 0 300 300" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="300" height="300" fill="#78909C" opacity="0.05"/>
    <filter id="stone-noise" x="0" y="0" width="300" height="300" filterUnits="userSpaceOnUse">
      <feTurbulence type="fractalNoise" baseFrequency="0.15" numOctaves="4" seed="1" result="noise"/>
      <feDisplacementMap in="SourceGraphic" in2="noise" scale="10" xChannelSelector="R" yChannelSelector="G"/>
    </filter>
    <rect width="300" height="300" filter="url(#stone-noise)" opacity="0.2" fill="#455A64"/>
  </svg>`,
  
  leatherTexture: `<svg width="300" height="300" viewBox="0 0 300 300" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="300" height="300" fill="#8D6E63" opacity="0.05"/>
    <filter id="leather-texture" x="0" y="0" width="300" height="300" filterUnits="userSpaceOnUse">
      <feTurbulence type="fractalNoise" baseFrequency="0.05" numOctaves="5" seed="3" result="noise"/>
      <feDisplacementMap in="SourceGraphic" in2="noise" scale="5" xChannelSelector="R" yChannelSelector="G"/>
    </filter>
    <rect width="300" height="300" filter="url(#leather-texture)" opacity="0.1" fill="#5D4037"/>
    <pattern id="smallDots" width="10" height="10" patternUnits="userSpaceOnUse">
      <circle cx="5" cy="5" r="0.5" fill="#3E2723" opacity="0.1"/>
    </pattern>
    <rect width="300" height="300" fill="url(#smallDots)"/>
  </svg>`
};

import React, { useEffect, useState } from 'react';
import { validateCatalogImages } from '../../utils/imageValidator';

/**
 * Componente para validar todas las imágenes del catálogo en modo de desarrollo
 * Este componente no renderiza nada visible, solo ejecuta la validación y muestra
 * mensajes en la consola.
 */
const ImageValidator: React.FC = () => {
  const [validationComplete, setValidationComplete] = useState(false);
  const [validationResults, setValidationResults] = useState<{
    valid: string[];
    invalid: string[];
    total: number;
  } | null>(null);

  useEffect(() => {
    // Solo ejecutar en modo de desarrollo
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    // Validar todas las imágenes del catálogo
    validateCatalogImages()
      .then(results => {
        setValidationResults(results);
        setValidationComplete(true);

        // Mostrar resumen en la consola
        console.group('📷 Validación de imágenes');
        console.info(`Total de imágenes: ${results.total}`);
        console.info(`✅ Imágenes válidas: ${results.valid.length}`);
        
        if (results.invalid.length > 0) {
          console.warn(`⚠️ Imágenes no encontradas: ${results.invalid.length}`);
          console.group('🔴 Detalles de imágenes no encontradas');
          results.invalid.forEach(path => {
            console.error(`Imagen no encontrada: ${path}`);
          });
          console.groupEnd();
        } else {
          console.info('✅ Todas las imágenes son válidas');
        }
        
        console.groupEnd();
      })
      .catch(error => {
        console.error('Error al validar imágenes:', error);
        setValidationComplete(true);
      });
  }, []);

  // Este componente no renderiza nada visible
  return null;
};

export default ImageValidator;

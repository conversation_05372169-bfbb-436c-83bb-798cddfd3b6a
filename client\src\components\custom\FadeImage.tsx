import React, { useState, useEffect, useRef, useCallback } from 'react';

interface FadeImageProps {
  images: string[];
  alt: string;
  interval?: number; // Intervalo en milisegundos
  initialDelay?: number; // Retraso inicial en milisegundos
  className?: string;
}

const FadeImage: React.FC<FadeImageProps> = ({
  images,
  alt,
  interval = 5000, // 5 segundos por defecto
  initialDelay = 500, // 0.5 segundos de retraso inicial por defecto (según requisito)
  className = "w-full h-full object-cover"
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [nextIndex, setNextIndex] = useState(1);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [currentOpacity, setCurrentOpacity] = useState(1);
  const [nextOpacity, setNextOpacity] = useState(0);
  const autoPlayTimerRef = useRef<NodeJS.Timeout | null>(null);
  const initialDelayTimerRef = useRef<NodeJS.Timeout | null>(null);
  const initialLoadRef = useRef<boolean>(true);
  const transitionDuration = 1000; // Duración de la transición en ms (1 segundo según requisito)
  const transitionTimersRef = useRef<NodeJS.Timeout[]>([]);

  // Función para limpiar todos los temporizadores de transición
  const clearTransitionTimers = useCallback(() => {
    transitionTimersRef.current.forEach(timer => clearTimeout(timer));
    transitionTimersRef.current = [];
  }, []);

  // Función para limpiar todos los temporizadores
  const clearAllTimers = useCallback(() => {
    if (autoPlayTimerRef.current) {
      clearInterval(autoPlayTimerRef.current);
      autoPlayTimerRef.current = null;
    }

    if (initialDelayTimerRef.current) {
      clearTimeout(initialDelayTimerRef.current);
      initialDelayTimerRef.current = null;
    }

    clearTransitionTimers();
  }, [clearTransitionTimers]);

  // Función para precargar imágenes para una transición más suave
  const preloadImages = useCallback(() => {
    if (images.length <= 1) return;

    images.forEach(src => {
      const img = new Image();
      img.src = src;
    });
  }, [images]);

  // Función centralizada para realizar la transición entre imágenes
  const performTransition = useCallback((targetIndex: number) => {
    if (isTransitioning || images.length <= 1) return;

    // Asegurarse de que el índice esté dentro del rango
    const nextIdx = (targetIndex + images.length) % images.length;

    // No hacer nada si ya estamos en esa imagen
    if (nextIdx === currentIndex) return;

    // Limpiar temporizadores de transición anteriores
    clearTransitionTimers();

    // Iniciar la transición
    setNextIndex(nextIdx);
    setIsTransitioning(true);

    // Configurar la opacidad inicial
    setCurrentOpacity(1);
    setNextOpacity(0);

    // Secuencia de transición (ajustada para duración de 1000ms)
    const timer1 = setTimeout(() => setNextOpacity(0.1), 50);
    transitionTimersRef.current.push(timer1);

    const timer2 = setTimeout(() => {
      setCurrentOpacity(0.9);
      setNextOpacity(0.3);
    }, 150);
    transitionTimersRef.current.push(timer2);

    const timer3 = setTimeout(() => {
      setCurrentOpacity(0.7);
      setNextOpacity(0.5);
    }, 300);
    transitionTimersRef.current.push(timer3);

    const timer4 = setTimeout(() => {
      setCurrentOpacity(0.5);
      setNextOpacity(0.7);
    }, 500);
    transitionTimersRef.current.push(timer4);

    const timer5 = setTimeout(() => {
      setCurrentOpacity(0.3);
      setNextOpacity(0.9);
    }, 700);
    transitionTimersRef.current.push(timer5);

    const timer6 = setTimeout(() => {
      setCurrentOpacity(0);
      setNextOpacity(1);
    }, 850);
    transitionTimersRef.current.push(timer6);

    // Finalizar la transición
    const timer7 = setTimeout(() => {
      setCurrentIndex(nextIdx);
      setCurrentOpacity(1);
      setNextOpacity(0);
      setIsTransitioning(false);
    }, transitionDuration + 25);
    transitionTimersRef.current.push(timer7);
  }, [currentIndex, images.length, isTransitioning, transitionDuration, clearTransitionTimers]);

  // Función para iniciar el temporizador de reproducción automática
  const startAutoPlayTimer = useCallback(() => {
    // Limpiar cualquier temporizador existente
    if (autoPlayTimerRef.current) {
      clearInterval(autoPlayTimerRef.current);
    }

    // Configurar un nuevo temporizador
    autoPlayTimerRef.current = setInterval(() => {
      if (!isTransitioning) {
        performTransition((currentIndex + 1) % images.length);
      }
    }, interval);
  }, [currentIndex, images.length, interval, isTransitioning, performTransition]);

  // Función para ir a una imagen específica (para los indicadores)
  const goToImage = useCallback((targetIndex: number) => {
    // Reiniciar el temporizador automático después de la interacción manual
    startAutoPlayTimer();

    // Realizar la transición
    performTransition(targetIndex);
  }, [performTransition, startAutoPlayTimer]);

  // Función para ir a la siguiente imagen
  const goToNext = useCallback(() => {
    // Reiniciar el temporizador automático después de la interacción manual
    startAutoPlayTimer();

    // Ir a la siguiente imagen
    performTransition((currentIndex + 1) % images.length);
  }, [currentIndex, images.length, performTransition, startAutoPlayTimer]);

  // Función para ir a la imagen anterior
  const goToPrev = useCallback(() => {
    // Reiniciar el temporizador automático después de la interacción manual
    startAutoPlayTimer();

    // Ir a la imagen anterior
    performTransition((currentIndex - 1 + images.length) % images.length);
  }, [currentIndex, images.length, performTransition, startAutoPlayTimer]);

  // Efecto para precargar imágenes al montar el componente
  useEffect(() => {
    preloadImages();
  }, [preloadImages]);

  // Efecto para configurar el intervalo de cambio de imágenes
  useEffect(() => {
    // Solo configurar el intervalo si hay más de una imagen
    if (images.length <= 1) return;

    // Limpiar todos los temporizadores existentes
    clearAllTimers();

    // Si es la carga inicial, aplicar el retraso inicial antes de comenzar
    if (initialLoadRef.current) {
      console.log('Configurando retraso inicial de', initialDelay, 'ms');

      initialDelayTimerRef.current = setTimeout(() => {
        console.log('Retraso inicial completado, iniciando reproducción automática');
        startAutoPlayTimer();
        initialLoadRef.current = false;
      }, initialDelay);
    } else {
      // Si no es la carga inicial, iniciar el temporizador inmediatamente
      startAutoPlayTimer();
    }

    // Limpiar al desmontar
    return () => {
      clearAllTimers();
    };
  }, [images.length, interval, initialDelay, clearAllTimers, startAutoPlayTimer]);

  // Si solo hay una imagen, mostrarla sin animación
  if (images.length === 1) {
    return <img src={images[0]} alt={alt} className={className} loading="lazy" />;
  }

  // Función para obtener la clase de opacidad
  const getOpacityClass = (opacity: number): string => {
    if (opacity === 0) return 'opacity-0';
    if (opacity === 1) return 'opacity-1';
    if (opacity >= 0.9) return 'opacity-09';
    if (opacity >= 0.7) return 'opacity-07';
    if (opacity >= 0.5) return 'opacity-05';
    if (opacity >= 0.3) return 'opacity-03';
    if (opacity >= 0.1) return 'opacity-01';
    return 'opacity-0';
  };

  return (
    <div className="carousel-container">
      <div className="fade-image-container group">
        {/* Imagen actual */}
        <img
          src={images[currentIndex]}
          alt={`${alt} - imagen ${currentIndex + 1}`}
          className={`${className} fade-image fade-image-current ${getOpacityClass(currentOpacity)} transition-transform duration-500 group-hover:scale-110`}
        />

        {/* Siguiente imagen (para la transición) */}
        <img
          src={images[nextIndex]}
          alt={`${alt} - imagen ${nextIndex + 1}`}
          className={`${className} fade-image fade-image-next ${getOpacityClass(nextOpacity)} transition-transform duration-500 group-hover:scale-110`}
        />
      </div>

      {/* Indicadores de posición (ocultos pero mantenidos para accesibilidad) */}
      <div className="carousel-indicators hidden">
        {images.map((_, index) => (
          <button
            key={index}
            type="button"
            className={`carousel-indicator ${index === currentIndex ? 'active' : ''}`}
            onClick={() => goToImage(index)}
            aria-label={`Ir a imagen ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default FadeImage;

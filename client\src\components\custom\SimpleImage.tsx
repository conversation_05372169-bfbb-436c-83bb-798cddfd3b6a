import React from 'react';

interface SimpleImageProps {
  src: string;
  alt: string;
  className?: string;
}

const SimpleImage: React.FC<SimpleImageProps> = ({ 
  src, 
  alt, 
  className = "w-full h-full object-cover" 
}) => {
  return (
    <img 
      src={src} 
      alt={alt} 
      className={className}
      onError={(e) => {
        console.error(`Error loading image: ${src}`);
        e.currentTarget.src = 'https://via.placeholder.com/400x300?text=Imagen+no+disponible';
      }}
    />
  );
};

export default SimpleImage;
